#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件整理脚本
"""

import os
import shutil

def organize_files():
    """整理文件到不同的文件夹"""
    
    # 创建分类文件夹
    folders = {
        "Excel处理工具": [],
        "AI监工系统": [],
        "技术文档": [],
        "测试文件": [],
        "打包相关": [],
        "其他工具": []
    }
    
    # 文件分类规则
    file_mappings = {
        # Excel处理工具相关
        "Excel处理工具": [
            "excel_pyqt5_app.py",
            "excel_app_macos.py", 
            "excel_app_v2.py",
            "excel_processor_app.py",
            "excel_simple_web.py",
            "excel_web_app.py",
            "simple_excel_app.py",
            "PyQt5版本使用说明.md",
            "使用说明.md",
            "更新说明.md",
            "第二次更新说明.md",
            "原样输出功能修复.md",
            "AI公式错误修复.md",
            "正则表达式最终修复.md",
            "正则表达式问题分析.md",
            "表头重复问题修复.md",
            "问题排查指南.md",
            "演示数据.xlsx",
            "excel_data_analysis.json",
            "excel_formulas.txt"
        ],
        
        # AI监工系统相关
        "AI监工系统": [
            "AI 监工告警日志-0613-0615.xlsx",
            "AI 监工告警日志-0613-0615_test.xlsx", 
            "AI 监工告警日志-0613-0615_副本.xlsx",
            "AI 监工告警日志-0613-0615_处理结果.xlsx",
            "AI监工2.0技术实现方案.md",
            "AI监工2.0流程图.md",
            "AI监工2.0流程图可视化.html",
            "AI外呼催收系统2.0流程图.html",
            "AI座舱系统架构规划.html",
            "众安保险金融事业部AI战役框架设计方案.md"
        ],
        
        # 技术文档相关
        "技术文档": [
            "optimization_suggestions.md",
            "optimized_prompt.md",
            "工程检查方法说明.md",
            "最终项目总结.md",
            "项目总结.md",
            "灵犀平台进阶--专业版.html",
            "灵犀平台进阶--深度使用技巧与经典案例拆解.pdf",
            "技能类型对比图表.html",
            "share-benefits-content.html",
            "share-benefits-slide.html",
            "API访问.png",
            "三类知识库介绍与对比.png",
            "与流程变量、全局变量同样的使用规则.png",
            "利用前置判断控制只执行一个节点.png",
            "利用后置处理设置值.png",
            "利用条件节点控制只执行一个节点.png",
            "异步调用不阻塞流程.png",
            "技能类型区别与对比.png",
            "节点并行缩短耗时.png",
            "文案检查助手的输出案例.png",
            "调用日志.png"
        ],
        
        # 测试文件相关
        "测试文件": [
            "test.py",
            "test_basic.py",
            "test_boolean.py",
            "test_condition.py",
            "test_debug.py",
            "test_echo.py",
            "test_freemarker.py",
            "test_freemarker_index.py",
            "test_functions.py",
            "test_input.json",
            "test_qq.py",
            "test_qq_debug.py",
            "test_regex.py",
            "test_with_file.py",
            "final_test.py",
            "final_test_qq.py",
            "simple_test.py",
            "minimal_test.py",
            "check_gui.py",
            "debug_full.py",
            "debug_qq.py",
            "diagnose_gui.py",
            "gui_test_simple.py",
            "minimal_gui_test.py",
            "demo_usage.py",
            "serialization_test.py",
            "工程检查测试用例.py"
        ],
        
        # 打包相关
        "打包相关": [
            "build.py",
            "requirements.txt",
            "install.bat",
            "install.sh", 
            "start.bat",
            "start.sh",
            "打包指南.md",
            "打包完成总结.md",
            "Excel数据处理工具.spec",
            "Excel数据处理工具_v2.0.zip",
            "Excel数据处理工具_分发包",
            "build",
            "dist"
        ],
        
        # 其他工具
        "其他工具": [
            "faq_processor.py",
            "freemarker_skill.py",
            "optimized_serialization.py",
            "parse_message.py",
            "serialization_optimization.py",
            "simple_wrapper.py",
            "text_processor.py",
            "tool.py",
            "启动应用.py",
            "工程检查优化版本.py"
        ]
    }
    
    # 创建文件夹
    for folder in file_mappings.keys():
        if not os.path.exists(folder):
            os.makedirs(folder)
            print(f"✅ 创建文件夹: {folder}")
    
    # 移动文件
    moved_count = 0
    for folder, files in file_mappings.items():
        for file in files:
            if os.path.exists(file):
                try:
                    if os.path.isdir(file):
                        # 移动文件夹
                        if os.path.exists(os.path.join(folder, file)):
                            shutil.rmtree(os.path.join(folder, file))
                        shutil.move(file, folder)
                    else:
                        # 移动文件
                        shutil.move(file, folder)
                    print(f"📁 移动: {file} → {folder}/")
                    moved_count += 1
                except Exception as e:
                    print(f"❌ 移动失败: {file} - {str(e)}")
    
    print(f"\n🎉 整理完成！共移动了 {moved_count} 个文件/文件夹")
    
    # 显示剩余文件
    remaining_files = []
    for item in os.listdir('.'):
        if item not in file_mappings.keys() and not item.startswith('.') and item != 'organize_files.py':
            remaining_files.append(item)
    
    if remaining_files:
        print(f"\n📋 根目录剩余文件:")
        for file in remaining_files:
            print(f"   - {file}")
    else:
        print(f"\n✨ 根目录已清理干净！")

if __name__ == "__main__":
    print("=" * 60)
    print("🗂️  文件整理工具")
    print("=" * 60)
    organize_files()
    print("=" * 60)
