========================================
    Excel数据处理工具 - Windows打包专用
========================================

📋 使用说明：
1. 这个文件夹包含了在Windows系统上打包所需的全部文件
2. 请按照下面的步骤操作，生成Windows可执行文件

🚀 快速开始（推荐）：
1. 双击运行 "一键打包.bat"
2. 等待自动完成（可能需要5-10分钟）
3. 完成后在 "dist" 文件夹中找到 "Excel数据处理工具.exe"

📋 详细步骤：

第一步：环境检查
- 确保已安装Python 3.7或更高版本
- 如果没有Python，请访问：https://www.python.org/downloads/
- 安装时务必勾选 "Add Python to PATH"

第二步：执行打包
- 双击运行 "一键打包.bat"
- 脚本会自动：
  * 检查Python环境
  * 安装PyInstaller
  * 安装项目依赖
  * 执行打包
  * 创建分发包

第三步：获取结果
打包成功后会生成：
- dist/Excel数据处理工具.exe （Windows可执行文件）
- Excel数据处理工具_分发包/ （完整分发包）

📁 文件说明：
- excel_pyqt5_app.py      主程序文件
- requirements.txt        Python依赖列表
- build.py               打包脚本
- version.txt            Windows版本信息
- 一键打包.bat            自动打包脚本
- 演示数据.xlsx          测试数据文件
- 使用说明.md            详细使用指南

⚠️ 注意事项：
1. 打包过程可能需要5-10分钟，请耐心等待
2. 首次运行会下载依赖包，需要网络连接
3. 杀毒软件可能会误报，请添加信任
4. 如果遇到问题，请查看控制台错误信息

🆘 常见问题：
Q: 提示找不到Python？
A: 请安装Python并确保添加到PATH环境变量

Q: 依赖安装失败？
A: 检查网络连接，或使用国内镜像：
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

Q: 打包失败？
A: 请将错误信息截图发送给技术支持

📞 技术支持：
如有问题请联系原开发者，并提供：
1. 错误截图
2. 控制台输出信息
3. 操作系统版本

========================================
祝您打包顺利！
========================================
