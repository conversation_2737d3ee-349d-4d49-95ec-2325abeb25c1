@echo off
chcp 65001
title Excel数据处理工具 - 程序测试
color 0B

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██           Excel数据处理工具 - 程序测试                 ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

echo 📋 开始测试打包后的程序...
echo.

REM 检查exe文件是否存在
if exist "dist\Excel数据处理工具.exe" (
    echo ✅ 找到可执行文件：dist\Excel数据处理工具.exe
    
    REM 获取文件信息
    for %%A in ("dist\Excel数据处理工具.exe") do (
        echo 📊 文件大小：%%~zA 字节
        echo 📅 修改时间：%%~tA
    )
    echo.
    
    echo 🚀 正在启动程序进行测试...
    echo 请在程序中进行以下测试：
    echo.
    echo 1. 界面是否正常显示
    echo 2. 能否选择Excel文件
    echo 3. 能否读取Excel数据
    echo 4. 各功能按钮是否响应
    echo.
    echo 按任意键启动程序...
    pause >nul
    
    REM 启动程序
    start "" "dist\Excel数据处理工具.exe"
    
    echo.
    echo 程序已启动，请进行功能测试...
    echo.
    echo 测试完成后，请反馈以下信息：
    echo ✓ 程序是否正常启动
    echo ✓ 界面是否显示正常
    echo ✓ 功能是否工作正常
    echo ✓ 是否有错误提示
    echo.
    
) else (
    echo ❌ 未找到可执行文件：dist\Excel数据处理工具.exe
    echo.
    echo 请先运行打包流程：
    echo 1. 双击 "一键打包.bat"
    echo 2. 等待打包完成
    echo 3. 再运行此测试脚本
    echo.
)

echo 按任意键退出...
pause >nul
