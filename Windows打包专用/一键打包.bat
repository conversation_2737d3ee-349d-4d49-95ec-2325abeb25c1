@echo off
chcp 65001
title Excel数据处理工具 - Windows一键打包
color 0A

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██        Excel数据处理工具 - Windows一键打包             ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

echo 📋 开始自动打包流程...
echo.

REM 第一步：检查Python环境
echo [1/6] 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境！
    echo.
    echo 请先安装Python 3.7或更高版本：
    echo 下载地址：https://www.python.org/downloads/
    echo 安装时请务必勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)
python --version
echo ✅ Python环境检查通过！
echo.

REM 第二步：检查必要文件
echo [2/6] 正在检查必要文件...
if not exist "excel_pyqt5_app.py" (
    echo ❌ 错误：找不到主程序文件 excel_pyqt5_app.py
    echo 请确保所有文件都在同一目录中
    pause
    exit /b 1
)
if not exist "requirements.txt" (
    echo ❌ 错误：找不到依赖文件 requirements.txt
    pause
    exit /b 1
)
echo ✅ 必要文件检查通过！
echo.

REM 第三步：安装PyInstaller
echo [3/6] 正在检查并安装PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败！
        echo 请检查网络连接或尝试手动安装：
        echo pip install pyinstaller
        pause
        exit /b 1
    )
    echo ✅ PyInstaller安装成功！
) else (
    echo ✅ PyInstaller已安装！
)
echo.

REM 第四步：安装项目依赖
echo [4/6] 正在安装项目依赖...
echo 这可能需要几分钟时间，请耐心等待...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败！
    echo.
    echo 尝试使用国内镜像重新安装...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if errorlevel 1 (
        echo ❌ 依赖安装仍然失败！
        echo 请检查网络连接或手动安装依赖
        pause
        exit /b 1
    )
)
echo ✅ 项目依赖安装成功！
echo.

REM 第五步：执行打包
echo [5/6] 正在执行打包...
echo 这可能需要5-10分钟，请耐心等待...
echo.
python build.py
if errorlevel 1 (
    echo ❌ 打包失败！
    echo 请检查上面的错误信息
    pause
    exit /b 1
)
echo.

REM 第六步：验证结果
echo [6/6] 正在验证打包结果...
if exist "dist\Excel数据处理工具.exe" (
    echo ✅ 打包成功！
    echo.
    echo 📁 生成的文件：
    echo    dist\Excel数据处理工具.exe
    echo.
    
    REM 获取文件大小
    for %%A in ("dist\Excel数据处理工具.exe") do (
        echo 📊 文件大小：%%~zA 字节
    )
    echo.
    
    if exist "Excel数据处理工具_分发包" (
        echo 📦 分发包：Excel数据处理工具_分发包\
        echo.
    )
    
    echo ████████████████████████████████████████████████████████████
    echo ██                                                        ██
    echo ██                    🎉 打包完成！                       ██
    echo ██                                                        ██
    echo ████████████████████████████████████████████████████████████
    echo.
    echo 🚀 现在您可以：
    echo 1. 测试运行：双击 dist\Excel数据处理工具.exe
    echo 2. 分发给用户：压缩 Excel数据处理工具_分发包 文件夹
    echo 3. 直接使用：将 .exe 文件复制到任何Windows电脑
    echo.
    echo 📋 分发包包含：
    echo    ✓ Excel数据处理工具.exe （主程序）
    echo    ✓ 使用说明.md （使用指南）
    echo    ✓ 演示数据.xlsx （测试数据）
    echo    ✓ README.txt （快速开始）
    echo.
    
) else (
    echo ❌ 打包失败：未找到生成的exe文件
    echo.
    echo 可能的原因：
    echo 1. 打包过程中出现错误
    echo 2. 杀毒软件阻止了文件生成
    echo 3. 磁盘空间不足
    echo.
    echo 建议：
    echo 1. 检查上面的错误信息
    echo 2. 暂时关闭杀毒软件重试
    echo 3. 确保有足够的磁盘空间（至少500MB）
)

echo.
echo 按任意键退出...
pause >nul
