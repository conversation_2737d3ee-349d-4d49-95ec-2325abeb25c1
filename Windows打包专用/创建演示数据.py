#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建演示数据文件
"""

import pandas as pd
import json

def create_demo_data():
    """创建演示数据"""
    
    # 创建示例数据
    data = {
        '姓名': ['张三', '李四', '王五', '赵六', '钱七'],
        '年龄': [25, 30, 35, 28, 32],
        '工资': [5000, 8000, 12000, 6500, 9500],
        '部门': ['技术部', '销售部', '财务部', '人事部', '市场部'],
        '入职时间': ['2020-01-15', '2019-06-20', '2018-03-10', '2021-09-05', '2020-11-30'],
        '个人信息': [
            '{"city": "北京", "education": "本科", "skills": ["Python", "Java"]}',
            '{"city": "上海", "education": "硕士", "skills": ["销售", "沟通"]}',
            '{"city": "广州", "education": "本科", "skills": ["财务", "Excel"]}',
            '{"city": "深圳", "education": "专科", "skills": ["招聘", "培训"]}',
            '{"city": "杭州", "education": "硕士", "skills": ["市场", "策划"]}'
        ],
        '备注': [
            '表现优秀，技术能力强',
            '销售业绩突出，客户关系良好',
            '财务工作细致，责任心强',
            '人事工作经验丰富',
            '市场敏感度高，创意能力强'
        ]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    df.to_excel('演示数据.xlsx', index=False, engine='openpyxl')
    print("✅ 演示数据文件创建成功：演示数据.xlsx")
    
    # 显示数据预览
    print("\n📋 数据预览：")
    print(df.head())
    
    print("\n💡 使用说明：")
    print("1. 执行算式示例：")
    print("   - 工资列：x*1.1 (涨薪10%)")
    print("   - 年龄列：x+1 (年龄加1)")
    print()
    print("2. 按字段取值示例：")
    print("   - 个人信息列：city|education|skills")
    print()
    print("3. AI写公式示例：")
    print("   - 计算工资的税后收入")
    print("   - 根据年龄判断年龄段")
    print()
    print("4. AI自定义任务示例：")
    print("   - 备注列：提取关键词")
    print("   - 姓名列：生成英文名")

if __name__ == "__main__":
    try:
        create_demo_data()
    except Exception as e:
        print(f"❌ 创建演示数据失败：{e}")
        print("请确保已安装pandas和openpyxl：")
        print("pip install pandas openpyxl")
