# -*- coding: utf-8 -*-
"""
Excel数据处理工具 - PyQt5版本
基于您提供的代码结构，使用PyQt5创建现代化界面
"""

import sys
import json
import logging
import requests
import os
import pandas as pd
from PyQt5.QtWidgets import (
    QApplication, QWidget, QPushButton, QFileDialog, QLineEdit, QVBoxLayout, 
    QHBoxLayout, QLabel, QMessageBox, QProgressBar, QGroupBox, QFormLayout, 
    QStyleFactory, QDesktopWidget, QTextEdit, QComboBox, QCheckBox, QScrollArea,
    QFrame, QSplitter, QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont, QPalette, QColor
from threading import Thread
import tempfile

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class DataProcessorThread(QThread):
    """数据处理线程"""
    progress_update = pyqtSignal(int)
    status_update = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, df, tasks, result_option, file_path):
        super().__init__()
        self.df = df
        self.tasks = tasks
        self.result_option = result_option
        self.file_path = file_path
        self.ai_formulas = {}  # 缓存AI生成的公式
    
    def run(self):
        try:
            self.status_update.emit("开始处理数据...")

            # 预先生成所有AI公式
            self.status_update.emit("正在生成AI公式...")
            self.generate_ai_formulas()

            # 创建结果数据框
            result_df = self.df.copy()
            total_rows = len(self.df)
            
            # 处理每一行
            for row_idx in range(total_rows):
                progress = int((row_idx / total_rows) * 100)
                self.progress_update.emit(progress)
                self.status_update.emit(f"正在处理第 {row_idx + 1}/{total_rows} 行")
                
                for task in self.tasks:
                    column = task['column']
                    method = task['method']
                    config = task['config']

                    if column not in self.df.columns:
                        continue

                    value = self.df.iloc[row_idx][column]

                    try:
                        result = self.process_single_value(value, method, config, column, row_idx)

                        # 特殊处理按字段取值，生成多列
                        if method == "按字段取值" and isinstance(result, dict):
                            for field_name, field_value in result.items():
                                new_column_name = f"{column}_{field_name}"
                                result_df.loc[row_idx, new_column_name] = field_value
                        elif method == "原样输出":
                            # 原样输出：创建新列存储原始值
                            new_column_name = f"{column}_原样输出"
                            result_df.loc[row_idx, new_column_name] = result
                        else:
                            # 其他处理方式生成单列
                            new_column_name = f"{column}_处理结果"
                            result_df.loc[row_idx, new_column_name] = result
                    except Exception as e:
                        logging.error(f"处理第{row_idx+1}行，列'{column}'时出错：{str(e)}")
                        if method == "按字段取值":
                            # 按字段取值出错时，为每个字段创建空列
                            field_list = config.split('|')
                            for field in field_list:
                                field = field.strip()
                                new_column_name = f"{column}_{field}"
                                result_df.loc[row_idx, new_column_name] = None
                        elif method == "原样输出":
                            # 原样输出出错时，创建对应的列
                            new_column_name = f"{column}_原样输出"
                            result_df.loc[row_idx, new_column_name] = None
                        else:
                            new_column_name = f"{column}_处理结果"
                            result_df.loc[row_idx, new_column_name] = None
            
            # 保存结果
            self.status_update.emit("正在保存结果...")
            output_path = self.save_results(result_df)
            
            self.progress_update.emit(100)
            self.finished.emit(True, f"处理完成！结果已保存到：{output_path}")
            
        except Exception as e:
            self.finished.emit(False, f"处理失败：{str(e)}")

    def generate_ai_formulas(self):
        """预先生成所有AI公式"""
        for task in self.tasks:
            if task['method'] == "AI写公式":
                column = task['column']
                config = task['config']

                # 获取列的示例值和列号
                sample_value = self.df.iloc[0][column] if len(self.df) > 0 else ""
                column_index = list(self.df.columns).index(column)
                column_letter = self.get_column_letter(column_index)

                # 调用AI生成公式
                formula = self.generate_single_ai_formula(config, column, column_letter, sample_value)
                self.ai_formulas[column] = formula

    def get_column_letter(self, column_index):
        """将列索引转换为Excel列字母"""
        result = ""
        while column_index >= 0:
            result = chr(column_index % 26 + ord('A')) + result
            column_index = column_index // 26 - 1
        return result

    def generate_single_ai_formula(self, description, column_name, column_letter, sample_value):
        """生成单个AI公式"""
        try:
            prompt = f"""请根据以下信息为Excel生成一个公式：

任务描述：{description}
列名：{column_name}
列号：{column_letter}
示例单元格值：{sample_value}

要求：
1. 只返回Excel公式，以=开头
2. 使用相对引用，如=A2+B2（不要使用绝对引用$A$2）
3. 公式应该适用于所有行，系统会自动调整行号
4. 不要包含任何解释文字

示例格式：=A2+B2 或 =SUM(A2:C2) 或 =IF(A2>100,"高","低")"""

            result = self.call_ai_api(prompt)
            if result and result.strip():
                formula = result.strip()
                if not formula.startswith('='):
                    formula = '=' + formula
                return formula
            else:
                return f"={column_letter}2"
        except Exception as e:
            logging.error(f"AI公式生成失败: {str(e)}")
            return f"={column_letter}2"
    
    def process_single_value(self, value, method, config, column, row_idx):
        """处理单个值"""
        if method == "原样输出":
            return value
        elif method == "执行算式":
            return self.process_formula(value, config)
        elif method == "按字段取值":
            return self.process_json_field(value, config)
        elif method == "AI写公式":
            return self.process_ai_formula(config, column, row_idx)
        elif method == "AI自定义任务":
            return self.process_ai_custom(value, config)
        else:
            return None
    
    def process_formula(self, value, formula):
        """执行算式"""
        try:
            x = float(value)
            formula_eval = formula.replace('x', str(x))
            result = eval(formula_eval)
            return result
        except:
            return None
    
    def process_json_field(self, value, fields):
        """提取JSON字段"""
        try:
            if isinstance(value, str):
                data = json.loads(value)
            else:
                data = value

            field_list = fields.split('|')
            results = {}
            for field in field_list:
                field = field.strip()
                if field in data:
                    results[field] = str(data[field])
                else:
                    results[field] = ""

            return results  # 返回字典，包含字段名和对应的值
        except:
            return None
    
    def process_ai_formula(self, description, column, row_idx):
        """AI生成公式（使用缓存的公式）"""
        try:
            print(f"🔍 process_ai_formula 调用:")
            print(f"   - column: {column}")
            print(f"   - row_idx: {row_idx}")
            print(f"   - ai_formulas keys: {list(self.ai_formulas.keys())}")

            if column in self.ai_formulas:
                # 使用缓存的公式，调整行号
                base_formula = self.ai_formulas[column]
                print(f"   - 找到缓存公式: {base_formula}")
                # 将公式中的行号从2调整为实际行号
                adjusted_formula = self.adjust_formula_row(base_formula, row_idx + 2)
                print(f"   - 调整后公式: {adjusted_formula}")
                return adjusted_formula
            else:
                # 如果没有缓存，返回默认公式
                print(f"   - 未找到缓存，返回默认公式")
                return f"=A{row_idx+2}"
        except Exception as e:
            print(f"❌ process_ai_formula 异常: {str(e)}")
            import traceback
            traceback.print_exc()
            logging.error(f"AI公式处理失败: {str(e)}")
            return f"=A{row_idx+2}"

    def adjust_formula_row(self, formula, target_row):
        """调整公式中的行号"""
        import re

        # 打印调试信息
        print(f"🔧 公式行号调整:")
        print(f"   - 原始公式: {formula}")
        print(f"   - 目标行号: {target_row}")

        # 使用函数替换来避免正则表达式组引用问题
        def replace_func(match):
            column_letters = match.group(1)
            return column_letters + str(target_row)

        # 更精确的匹配模式：字母+2，且2后面不是数字（避免匹配1000中的2）
        pattern = r'([A-Z]+)2(?!\d)'
        adjusted = re.sub(pattern, replace_func, formula)

        print(f"   - 调整后公式: {adjusted}")
        print("-" * 40)

        return adjusted
    
    def process_ai_custom(self, value, description):
        """AI自定义处理"""
        try:
            prompt = f"""请根据以下任务描述处理给定的内容：
任务描述：{description}
待处理内容：{value}

请直接返回处理结果，不要包含其他解释。"""
            
            result = self.call_ai_api(prompt)
            return result if result else f"AI处理: {description[:20]}..."
        except:
            return f"AI处理: {description[:20]}..."
    
    def call_ai_api(self, prompt):
        """调用AI API"""
        try:
            url = "http://za-aigc-platform.test.za.biz/bots/lytestrobot/lycommonpromptskill/execute"
            headers = {
                'Content-Type': 'application/json',
                'access-key': '20240620180544TIVEBMKFIRTPAEMUQF'
            }

            data = {
                "model": "qwen-72b",
                "prompt": prompt
            }

            # 打印请求入参日志
            print("=" * 80)
            print("🚀 AI API 请求开始")
            print(f"📍 URL: {url}")
            print(f"📋 Headers: {headers}")
            print(f"📝 Request Data:")
            print(f"   - model: {data['model']}")
            print(f"   - prompt: {data['prompt'][:200]}..." if len(data['prompt']) > 200 else f"   - prompt: {data['prompt']}")
            print("=" * 80)

            response = requests.post(url, headers=headers, json=data, timeout=30)

            # 打印响应状态
            print(f"📊 Response Status: {response.status_code}")
            print(f"📄 Response Headers: {dict(response.headers)}")

            response.raise_for_status()
            result = response.json()

            # 打印响应出参日志
            print(f"✅ Response JSON:")
            print(f"   - Full Response: {result}")

            if 'data' in result:
                print(f"   - Extracted Data: {result['data']}")
                print("🎉 AI API 请求成功")
                print("=" * 80)
                return result['data'].strip()
            else:
                print("⚠️  Response 中没有 'data' 字段")
                print("=" * 80)
                return ""

        except requests.RequestException as e:
            print(f"❌ 网络请求失败: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"   - Response Status: {e.response.status_code}")
                print(f"   - Response Text: {e.response.text}")
            print("=" * 80)
            logging.error(f"AI API网络请求失败: {str(e)}")
            return ""
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {str(e)}")
            print(f"   - Raw Response: {response.text}")
            print("=" * 80)
            logging.error(f"AI API JSON解析失败: {str(e)}")
            return ""
        except Exception as e:
            print(f"❌ 其他错误: {str(e)}")
            print("=" * 80)
            logging.error(f"AI API调用失败: {str(e)}")
            return ""
    
    def save_results(self, result_df):
        """保存结果"""
        base_name = os.path.splitext(self.file_path)[0]

        if self.result_option == "new_workbook":
            # 新建工作簿：只保存结果列
            result_columns = [col for col in result_df.columns if col not in self.df.columns]
            if result_columns:
                output_df = result_df[result_columns]
                output_path = f"{base_name}_处理结果.xlsx"
            else:
                # 如果没有新列，保存所有数据
                output_df = result_df
                output_path = f"{base_name}_处理结果.xlsx"
        else:
            # 追加在后方：保留原始数据+结果数据
            output_df = result_df
            output_path = f"{base_name}_完整结果.xlsx"

        output_df.to_excel(output_path, index=False)
        return output_path


class ColumnConfigWidget(QWidget):
    """列配置组件"""
    
    def __init__(self, column_name, first_value):
        super().__init__()
        self.column_name = column_name
        self.first_value = first_value
        self.init_ui()
    
    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 列名标签
        self.name_label = QLabel(self.column_name)
        self.name_label.setFixedWidth(120)
        self.name_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(self.name_label)
        
        # 第一行内容按钮
        display_value = self.first_value[:30] + "..." if len(self.first_value) > 30 else self.first_value
        self.content_btn = QPushButton(display_value)
        self.content_btn.setFixedWidth(200)
        self.content_btn.clicked.connect(self.show_full_content)
        layout.addWidget(self.content_btn)
        
        # 是否处理复选框
        self.process_check = QCheckBox("处理")
        self.process_check.stateChanged.connect(self.toggle_controls)
        layout.addWidget(self.process_check)
        
        # 处理方式下拉框
        self.method_combo = QComboBox()
        self.method_combo.addItems(["", "原样输出", "执行算式", "按字段取值", "AI写公式", "AI自定义任务"])
        self.method_combo.setFixedWidth(120)
        self.method_combo.setEnabled(False)
        self.method_combo.currentTextChanged.connect(self.update_hint)
        layout.addWidget(self.method_combo)
        
        # 任务详情输入框
        self.config_edit = QLineEdit()
        self.config_edit.setPlaceholderText("请选择处理方式")
        self.config_edit.setEnabled(False)
        layout.addWidget(self.config_edit)
        
        self.setLayout(layout)
    
    def show_full_content(self):
        """显示完整内容"""
        msg = QMessageBox()
        msg.setWindowTitle(f"列 '{self.column_name}' 的内容")
        msg.setText(self.first_value)
        msg.exec_()
    
    def toggle_controls(self, state):
        """切换控件状态"""
        enabled = state == Qt.Checked
        self.method_combo.setEnabled(enabled)
        self.config_edit.setEnabled(enabled)
        if enabled:
            self.update_hint()
        else:
            self.config_edit.setPlaceholderText("请选择处理方式")
    
    def update_hint(self):
        """更新提示信息"""
        method = self.method_combo.currentText()
        hints = {
            "原样输出": "原样输出该列",
            "执行算式": "在此处编写算式，使用 x 代表表格内容，如 (x+2)*3",
            "按字段取值": "单元格内容必须是JSON，在此处编写需提取内容的字段名，提取多个时使用|分割字段名，如：msg|name|value",
            "AI写公式": "在此处使用文字描述公式功能，AI 会自行编写Excel公式",
            "AI自定义任务": "在此处使用文字描述待执行的任务，注意：该方式，AI 会顺序执行每一行的单元格内容，耗时耗钱"
        }
        
        if method in hints:
            self.config_edit.setPlaceholderText(hints[method])
        else:
            self.config_edit.setPlaceholderText("请选择处理方式")
    
    def get_config(self):
        """获取配置"""
        if not self.process_check.isChecked():
            return None
        
        method = self.method_combo.currentText()
        config = self.config_edit.text().strip()
        
        if not method or not config:
            return None
        
        return {
            'column': self.column_name,
            'method': method,
            'config': config
        }


class ExcelProcessorPyQt5(QWidget):
    """Excel数据处理工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.df = None
        self.file_path = None
        self.column_widgets = []
        self.init_ui()
        self.center_window()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('Excel数据处理工具 - PyQt5版')
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                font-size: 10pt;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel('📊 Excel数据处理工具')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #0078d4; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 文件选择区域
        self.create_file_section(main_layout)
        
        # 结果存储选项
        self.create_result_section(main_layout)
        
        # 任务配置区域
        self.create_task_section(main_layout)
        
        # 控制按钮区域
        self.create_control_section(main_layout)
        
        # 状态区域
        self.create_status_section(main_layout)
        
        self.setLayout(main_layout)
    
    def center_window(self):
        """窗口居中显示"""
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def create_file_section(self, main_layout):
        """创建文件选择区域"""
        file_group = QGroupBox("📁 文件选择")
        file_layout = QVBoxLayout()
        
        # 文件路径显示
        path_layout = QHBoxLayout()
        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("color: #666666; padding: 5px;")
        path_layout.addWidget(self.file_path_label)
        
        # 按钮
        btn_layout = QHBoxLayout()
        self.select_btn = QPushButton("📂 选择Excel文件")
        self.select_btn.clicked.connect(self.select_file)
        btn_layout.addWidget(self.select_btn)
        
        self.read_btn = QPushButton("📖 读取Excel")
        self.read_btn.clicked.connect(self.read_excel)
        self.read_btn.setEnabled(False)
        btn_layout.addWidget(self.read_btn)
        
        btn_layout.addStretch()
        
        file_layout.addLayout(path_layout)
        file_layout.addLayout(btn_layout)
        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)
    
    def create_result_section(self, main_layout):
        """创建结果存储选项"""
        result_group = QGroupBox("💾 结果存储")
        result_layout = QHBoxLayout()
        
        self.result_new = QCheckBox("新建工作簿")
        self.result_new.setChecked(True)
        result_layout.addWidget(self.result_new)

        self.result_append = QCheckBox("追加在后方")
        result_layout.addWidget(self.result_append)
        
        # 互斥选择
        self.result_new.stateChanged.connect(lambda: self.result_append.setChecked(False) if self.result_new.isChecked() else None)
        self.result_append.stateChanged.connect(lambda: self.result_new.setChecked(False) if self.result_append.isChecked() else None)
        
        result_layout.addStretch()
        result_group.setLayout(result_layout)
        main_layout.addWidget(result_group)
    
    def create_task_section(self, main_layout):
        """创建任务配置区域"""
        task_group = QGroupBox("🔧 任务配置")
        task_layout = QVBoxLayout()
        
        # 说明标签
        self.task_info_label = QLabel("请先选择并读取Excel文件")
        self.task_info_label.setStyleSheet("color: #0078d4; font-style: italic; padding: 5px;")
        task_layout.addWidget(self.task_info_label)
        
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setMinimumHeight(300)
        
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_area.setWidget(self.scroll_widget)
        
        task_layout.addWidget(self.scroll_area)
        task_group.setLayout(task_layout)
        main_layout.addWidget(task_group)
    
    def create_control_section(self, main_layout):
        """创建控制按钮区域"""
        control_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("🧪 试运行")
        self.test_btn.clicked.connect(self.test_run)
        self.test_btn.setEnabled(False)
        control_layout.addWidget(self.test_btn)
        
        self.process_btn = QPushButton("▶ 开始执行")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setEnabled(False)
        control_layout.addWidget(self.process_btn)
        
        control_layout.addStretch()
        
        help_btn = QPushButton("❓ 帮助")
        help_btn.clicked.connect(self.show_help)
        control_layout.addWidget(help_btn)
        
        main_layout.addLayout(control_layout)
    
    def create_status_section(self, main_layout):
        """创建状态区域"""
        status_group = QGroupBox("📊 状态信息")
        status_layout = QVBoxLayout()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #0078d4; padding: 5px;")
        status_layout.addWidget(self.status_label)
        
        status_group.setLayout(status_layout)
        main_layout.addWidget(status_group)

    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel文件",
            "",
            "Excel files (*.xlsx *.xls);;All files (*.*)"
        )

        if file_path:
            self.file_path = file_path
            filename = os.path.basename(file_path)
            self.file_path_label.setText(f"已选择: {filename}")
            self.read_btn.setEnabled(True)
            self.status_label.setText(f"已选择文件: {filename}")

    def read_excel(self):
        """读取Excel文件"""
        if not self.file_path:
            QMessageBox.warning(self, "警告", "请先选择Excel文件")
            return

        try:
            self.status_label.setText("正在读取Excel文件...")
            QApplication.processEvents()

            # 读取Excel文件
            self.df = pd.read_excel(self.file_path)

            # 创建列配置界面
            self.create_column_configs()

            # 更新界面状态
            self.task_info_label.setText("请配置需要处理的列，然后使用试运行验证")
            self.test_btn.setEnabled(True)
            self.process_btn.setEnabled(True)

            self.status_label.setText(f"成功读取 {len(self.df)} 行 {len(self.df.columns)} 列数据")

            QMessageBox.information(
                self,
                "成功",
                f"成功读取Excel文件\n行数: {len(self.df)}\n列数: {len(self.df.columns)}"
            )

        except Exception as e:
            self.status_label.setText("读取失败")
            QMessageBox.critical(self, "错误", f"读取Excel文件失败:\n{str(e)}")

    def create_column_configs(self):
        """创建列配置界面"""
        # 清空现有配置和所有子组件
        for widget in self.column_widgets:
            widget.setParent(None)
        self.column_widgets.clear()

        # 清空滚动布局中的所有组件
        while self.scroll_layout.count():
            child = self.scroll_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

        # 添加表头说明
        header_layout = QHBoxLayout()
        headers = ["列名", "第一行内容", "是否处理", "处理方式", "任务详情"]
        widths = [120, 200, 60, 120, 300]

        for header, width in zip(headers, widths):
            label = QLabel(header)
            label.setFixedWidth(width)
            label.setStyleSheet("font-weight: bold; color: #0078d4; padding: 5px;")
            header_layout.addWidget(label)

        header_widget = QWidget()
        header_widget.setLayout(header_layout)
        self.scroll_layout.addWidget(header_widget)

        # 为每列创建配置组件
        for column in self.df.columns:
            first_value = str(self.df.iloc[0][column]) if len(self.df) > 0 else ""
            config_widget = ColumnConfigWidget(column, first_value)
            self.scroll_layout.addWidget(config_widget)
            self.column_widgets.append(config_widget)

        # 添加弹性空间
        self.scroll_layout.addStretch()

    def get_selected_tasks(self):
        """获取选中的任务配置"""
        tasks = []
        for widget in self.column_widgets:
            config = widget.get_config()
            if config:
                tasks.append(config)
        return tasks

    def test_run(self):
        """试运行"""
        if self.df is None:
            QMessageBox.warning(self, "警告", "请先读取Excel文件")
            return

        tasks = self.get_selected_tasks()
        if not tasks:
            QMessageBox.warning(self, "警告", "请至少选择一列进行处理")
            return

        try:
            self.status_label.setText("正在试运行...")
            QApplication.processEvents()

            # 处理第一行数据
            results = []
            for task in tasks:
                column = task['column']
                method = task['method']
                config = task['config']

                value = self.df.iloc[0][column]

                # 不捕获异常，让错误暴露出来
                # 创建临时处理线程来处理单个值
                processor = DataProcessorThread(None, None, None, None)

                # 对于AI写公式，需要先生成公式
                if method == "AI写公式":
                    processor.ai_formulas[column] = processor.generate_single_ai_formula(config, column, "A", str(value))

                result = processor.process_single_value(value, method, config, column, 0)

                # 特殊处理按字段取值的结果显示
                if method == "按字段取值" and isinstance(result, dict):
                    for field_name, field_value in result.items():
                        results.append({
                            'column': f"{column}_{field_name}",
                            'original': str(value)[:50] + "..." if len(str(value)) > 50 else str(value),
                            'result': str(field_value) if field_value is not None else '空值'
                        })
                elif method == "原样输出":
                    results.append({
                        'column': f"{column}_原样输出",
                        'original': str(value)[:50] + "..." if len(str(value)) > 50 else str(value),
                        'result': str(result) if result is not None else '处理失败（空值）'
                    })
                else:
                    results.append({
                        'column': column,
                        'original': str(value)[:50] + "..." if len(str(value)) > 50 else str(value),
                        'result': str(result) if result is not None else '处理失败（空值）'
                    })

            # 显示试运行结果
            self.show_test_results(results)
            self.status_label.setText("试运行完成")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"试运行失败:\n{str(e)}")
            self.status_label.setText("试运行失败")

    def show_test_results(self, results):
        """显示试运行结果"""
        dialog = QWidget()
        dialog.setWindowTitle("试运行结果")
        dialog.setGeometry(200, 200, 800, 400)
        dialog.setWindowModality(Qt.ApplicationModal)

        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("试运行结果（第一行数据处理结果）")
        title_label.setStyleSheet("font-size: 14pt; font-weight: bold; color: #0078d4; margin: 10px;")
        layout.addWidget(title_label)

        # 结果表格
        table = QTableWidget()
        table.setRowCount(len(results))
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["列名", "原始值", "处理结果"])

        # 设置表格内容
        for i, result in enumerate(results):
            table.setItem(i, 0, QTableWidgetItem(result['column']))
            table.setItem(i, 1, QTableWidgetItem(result['original']))
            table.setItem(i, 2, QTableWidgetItem(result['result']))

        # 调整列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)

        layout.addWidget(table)

        # 按钮
        btn_layout = QHBoxLayout()

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        btn_layout.addWidget(close_btn)

        continue_btn = QPushButton("继续执行")
        continue_btn.clicked.connect(lambda: [dialog.close(), self.start_processing()])
        btn_layout.addWidget(continue_btn)

        btn_layout.addStretch()
        layout.addLayout(btn_layout)

        dialog.setLayout(layout)
        dialog.show()

        # 保持对话框引用
        self.test_dialog = dialog

    def start_processing(self):
        """开始处理"""
        if self.df is None:
            QMessageBox.warning(self, "警告", "请先读取Excel文件")
            return

        tasks = self.get_selected_tasks()
        if not tasks:
            QMessageBox.warning(self, "警告", "请至少选择一列进行处理")
            return

        # 确定结果存储方式
        result_option = "new_workbook" if self.result_new.isChecked() else "append"

        # 禁用按钮
        self.test_btn.setEnabled(False)
        self.process_btn.setEnabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 启动处理线程
        self.worker_thread = DataProcessorThread(self.df, tasks, result_option, self.file_path)
        self.worker_thread.progress_update.connect(self.progress_bar.setValue)
        self.worker_thread.status_update.connect(self.status_label.setText)
        self.worker_thread.finished.connect(self.on_processing_finished)
        self.worker_thread.start()

    def on_processing_finished(self, success, message):
        """处理完成回调"""
        # 恢复按钮状态
        self.test_btn.setEnabled(True)
        self.process_btn.setEnabled(True)

        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 显示结果
        if success:
            # 提取文件路径
            if "结果已保存到：" in message:
                file_path = message.split("结果已保存到：")[1].strip()
                self.show_success_dialog(message, file_path)
            else:
                QMessageBox.information(self, "成功", message)
            self.status_label.setText("处理完成")
        else:
            QMessageBox.critical(self, "失败", message)
            self.status_label.setText("处理失败")

    def show_success_dialog(self, message, file_path):
        """显示成功对话框，包含打开文件按钮"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle("处理完成")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)

        # 添加自定义按钮
        open_btn = msg_box.addButton("📂 打开文件", QMessageBox.ActionRole)
        close_btn = msg_box.addButton("关闭", QMessageBox.AcceptRole)

        msg_box.exec_()

        # 检查用户点击了哪个按钮
        if msg_box.clickedButton() == open_btn:
            self.open_file(file_path)

    def open_file(self, file_path):
        """打开文件"""
        try:
            import subprocess
            import sys

            if sys.platform == "darwin":  # macOS
                subprocess.call(["open", file_path])
            elif sys.platform == "win32":  # Windows
                os.startfile(file_path)
            else:  # Linux
                subprocess.call(["xdg-open", file_path])

            self.status_label.setText(f"已打开文件: {os.path.basename(file_path)}")
        except Exception as e:
            QMessageBox.warning(self, "警告", f"无法打开文件：{str(e)}")
            logging.error(f"打开文件失败: {str(e)}")

    def show_help(self):
        """显示帮助"""
        help_text = """Excel数据处理工具使用说明

1. 文件操作
   • 点击"选择Excel文件"选择要处理的文件
   • 点击"读取Excel"加载文件内容

2. 配置任务
   • 勾选需要处理的列
   • 选择处理方式
   • 填写具体配置

3. 处理方式说明
   • 原样输出：原样输出该列数据
     示例：直接复制列内容

   • 执行算式：数学运算，用x代表单元格值
     示例：(x+2)*3, x/100, x**2

   • 按字段取值：从JSON中提取字段，生成多列
     示例：name|age|city（会生成3个新列）

   • AI写公式：生成Excel公式
     示例：计算A列和B列的和

   • AI自定义任务：智能文本处理
     示例：提取关键信息并总结

4. 执行处理
   • 使用"试运行"验证配置
   • 点击"开始执行"进行批量处理

5. 结果存储
   • 新建工作簿：创建新文件，只包含处理结果
   • 追加在后方：创建新文件，包含原始数据+处理结果"""

        QMessageBox.information(self, "使用帮助", help_text)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle(QStyleFactory.create('Fusion'))

    # 设置应用程序图标和信息
    app.setApplicationName("Excel数据处理工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("AI Tools")

    # 创建主窗口
    window = ExcelProcessorPyQt5()
    window.show()

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
