移动光标
h/j/k/l：左/下/上/右

w / b：向前/后跳一个单词

0 / H / L：行首/行首/行尾

gg / G：文件首/尾

J / K：向下/上翻页

:{行号}：跳转到指定行（如 :10）

插入模式
i / a：光标前/后插入

I / A：行首/行尾插入

o / O：下方/上方插入新行

删除与撤销
x：删除当前字符

dw / d$：删除到单词尾/行尾

dd：删除整行

u / Ctrl + r：撤销/重做



复制与粘贴
yy：复制当前行

p / P：粘贴到后/前

搜索与替换
/关键词：向下搜索（n/N 跳转）

?关键词：向上搜索

:%s/old/new/g：全局替换（加 c 确认）

文件操作
:w：保存

:q：退出（加 ! 强制）

:wq / :x：保存并退出

:e 文件名：打开文件

分屏与标签
:sp / :vsp：水平/垂直分屏

Ctrl + w + 方向：切换分屏

:tabnew：新建标签页

gt / gT：切换标签页

其他高效操作
.：重复上一次操作

*：高亮当前单词并搜索

Ctrl + v：块选择模式（用于多行编辑）

>> / <<：缩进/反缩进行

提示：Vim 的强大在于组合命令，例如：

d5w：删除 5 个单词

ci"：删除引号内内容并进入插入模式