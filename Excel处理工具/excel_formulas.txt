Excel公式提取JSON字段

假设JSON数据在J2单元格，以下是各列的公式：

K列 (sessionId):
=MID(J2,SEARCH("""sessionId"":""",J2)+13,<PERSON>ARCH(""",""",J2,<PERSON>ARCH("""sessionId"":""",J2)+13)-<PERSON>ARCH("""sessionId"":""",J2)-13)

L列 (msg):
=MID(J2,SEARCH("""msg"":""",J2)+7,SEARCH(""",""playStatus""",J2)-SEARCH("""msg"":""",J2)-7)

M列 (history):
=MID(J2,SEARCH("""history"":""",J2)+11,SEARCH(""",""sessionId""",J2)-SEARCH("""history"":""",J2)-11)

N列 (空列):
=""

O列 (mergeTag):
=MID(J2,<PERSON>AR<PERSON>("""mergeTag"":""",J2)+12,<PERSON>AR<PERSON>(""",""",J2,<PERSON><PERSON><PERSON>("""mergeTag"":""",J2)+12)-<PERSON>AR<PERSON>("""mergeTag"":""",J2)-12)

P列 (playStatus):
=MID(J2,SEARCH("""playStatus"":""",J2)+14,SEARCH(""",""",J2,SEARCH("""playStatus"":""",J2)+14)-SEARCH("""playStatus"":""",J2)-14)

注意事项：
1. 这些公式假设JSON格式固定，字段顺序不变
2. 如果JSON中包含转义字符或特殊字符，可能需要调整公式
3. 复制公式到其他行时，记得修改行号（如J2改为J3等）

更简化的版本（如果Excel支持TEXTBEFORE和TEXTAFTER函数）：

K列 (sessionId):
=TEXTBEFORE(TEXTAFTER(J2,"""sessionId"":"""),""",""")

L列 (msg):
=TEXTBEFORE(TEXTAFTER(J2,"""msg"":"""),""",""playStatus""")

M列 (history):
=TEXTBEFORE(TEXTAFTER(J2,"""history"":"""),""",""sessionId""")

N列 (空列):
=""

O列 (mergeTag):
=TEXTBEFORE(TEXTAFTER(J2,"""mergeTag"":"""),""",""")

P列 (playStatus):
=TEXTBEFORE(TEXTAFTER(J2,"""playStatus"":"""),""",""")
