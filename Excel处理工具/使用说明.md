# Excel数据处理工具使用说明

## 功能概述
这是一个带UI界面的Python应用，用于处理Excel文件中的数据。支持4种处理方式：
1. 执行算式
2. 按字段取值
3. AI写公式
4. AI自定义任务

## 使用步骤

### 1. 启动应用
```bash
python3 excel_processor_app.py
```

### 2. 选择和读取Excel文件
- 点击"选择Excel文件"按钮，选择要处理的Excel文件
- 点击"读取Excel"按钮，加载文件数据

### 3. 配置结果存储方式
选择处理结果的存储位置：
- **表格后方空白的列**：在原Excel文件中添加新列存储结果
- **新建工作簿**：创建新的Excel文件存储结果

### 4. 配置任务详情
对于每一列，可以进行以下配置：
- **表头**：显示列名
- **第一行内容**：显示该列第一行的数据（点击可查看完整内容）
- **是否处理**：勾选表示需要处理该列
- **处理方式**：选择处理方法
- **任务详情**：根据处理方式填写具体参数

### 5. 处理方式详解

#### 执行算式
- **用途**：对数值列进行数学运算
- **输入示例**：`(x+2)*3` 或 `x/100`
- **说明**：使用 `x` 代表当前单元格的值

#### 按字段取值
- **用途**：从JSON数据中提取特定字段
- **输入示例**：`msg|name|value`
- **说明**：使用 `|` 分隔多个字段名

#### AI写公式
- **用途**：让AI生成Excel公式
- **输入示例**：`计算A列和B列的和`
- **说明**：用自然语言描述需要的公式功能

#### AI自定义任务
- **用途**：让AI处理复杂的文本任务
- **输入示例**：`提取文本中的关键信息并总结`
- **说明**：会逐行调用AI处理，耗时较长

### 6. 执行处理
- **试运行**：处理第一行数据，检查配置是否正确
- **开始执行**：处理所有数据

## 注意事项
1. AI功能需要网络连接
2. 大量数据处理可能需要较长时间
3. 建议先使用"试运行"功能验证配置
4. 处理过程中会显示进度条和状态信息

## 示例数据
当前Excel文件包含以下列：
- 调用链ID、请求ID、会话ID
- 机器人编号、机器人名、标签
- 技能编号、技能名、敏感词列表
- 请求内容、响应内容、用户姓名
- 请求时间、耗时、模型
- 反馈结果、反馈来源、反馈时间、反馈标签、反馈描述

## 常见用例
1. **计算耗时统计**：对"耗时"列执行算式 `x/1000` 转换为秒
2. **提取JSON字段**：从"请求内容"中提取特定字段
3. **生成统计公式**：让AI生成汇总计算公式
4. **文本分析**：让AI分析"反馈描述"内容
