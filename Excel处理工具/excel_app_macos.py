#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据处理工具 - macOS优化版本
专门针对macOS系统的GUI显示问题进行优化
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import json
import logging
from threading import Thread

# macOS特定设置
if sys.platform == "darwin":
    # 设置macOS特定的环境变量
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    # 确保使用系统的Tk
    import tkinter
    tkinter._test()

class ExcelProcessorMacOS:
    def __init__(self):
        # 创建根窗口
        self.root = tk.Tk()
        
        # macOS特定设置
        if sys.platform == "darwin":
            # 设置窗口属性
            self.root.tk.call('tk', 'scaling', 1.0)
            # 强制窗口显示在前台
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        
        self.root.title("Excel数据处理工具 - macOS版")
        self.root.geometry("1000x700")
        
        # 数据存储
        self.df = None
        self.file_path = None
        self.task_configs = {}
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        # 创建界面
        self.setup_ui()
        
        print("macOS版Excel应用初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建菜单栏（macOS风格）
        self.create_menubar()
        
        # 主容器
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题区域
        title_frame = ttk.Frame(self.main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(title_frame, text="📊 Excel数据处理工具", 
                               font=("SF Pro Display", 18, "bold"))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="支持算式执行、JSON提取、AI公式生成等功能", 
                                  font=("SF Pro Display", 11), foreground="gray")
        subtitle_label.pack(pady=(5, 0))
        
        # 文件选择区域
        self.create_file_section()
        
        # 选项区域
        self.create_options_section()
        
        # 任务配置区域
        self.create_task_section()
        
        # 控制按钮区域
        self.create_controls_section()
        
        # 状态区域
        self.create_status_section()
        
        print("UI组件创建完成")
    
    def create_menubar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开Excel文件...", command=self.select_file, accelerator="Cmd+O")
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit, accelerator="Cmd+Q")
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        
        # 绑定快捷键
        self.root.bind('<Command-o>', lambda e: self.select_file())
        self.root.bind('<Command-q>', lambda e: self.root.quit())
    
    def create_file_section(self):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(self.main_frame, text="📁 文件选择", padding=15)
        file_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 文件信息显示
        info_frame = ttk.Frame(file_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_var = tk.StringVar(value="未选择文件")
        file_label = ttk.Label(info_frame, textvariable=self.file_var, 
                              font=("SF Pro Display", 12))
        file_label.pack(side=tk.LEFT)
        
        # 按钮区域
        button_frame = ttk.Frame(file_frame)
        button_frame.pack(fill=tk.X)
        
        select_btn = ttk.Button(button_frame, text="📂 选择Excel文件", 
                               command=self.select_file)
        select_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        read_btn = ttk.Button(button_frame, text="📖 读取数据", 
                             command=self.read_excel)
        read_btn.pack(side=tk.LEFT)
        
        # 文件信息
        self.file_info_var = tk.StringVar(value="")
        info_label = ttk.Label(button_frame, textvariable=self.file_info_var, 
                              foreground="gray")
        info_label.pack(side=tk.RIGHT)
    
    def create_options_section(self):
        """创建选项区域"""
        options_frame = ttk.LabelFrame(self.main_frame, text="⚙️ 处理选项", padding=15)
        options_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 结果存储选项
        result_frame = ttk.Frame(options_frame)
        result_frame.pack(fill=tk.X)
        
        ttk.Label(result_frame, text="结果存储位置:", 
                 font=("SF Pro Display", 11, "bold")).pack(side=tk.LEFT)
        
        self.result_option = tk.StringVar(value="new_workbook")
        
        radio_frame = ttk.Frame(result_frame)
        radio_frame.pack(side=tk.LEFT, padx=(20, 0))
        
        ttk.Radiobutton(radio_frame, text="新建工作簿", 
                       variable=self.result_option, value="new_workbook").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(radio_frame, text="追加到原文件", 
                       variable=self.result_option, value="append").pack(side=tk.LEFT)
    
    def create_task_section(self):
        """创建任务配置区域"""
        task_frame = ttk.LabelFrame(self.main_frame, text="🔧 任务配置", padding=15)
        task_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 说明文本
        self.task_info_var = tk.StringVar(value="请先选择并读取Excel文件")
        info_label = ttk.Label(task_frame, textvariable=self.task_info_var, 
                              font=("SF Pro Display", 11), foreground="blue")
        info_label.pack(pady=(0, 10))
        
        # 滚动区域
        canvas_frame = ttk.Frame(task_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(canvas_frame, height=200, bg="white")
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始提示
        self.show_initial_message()
    
    def show_initial_message(self):
        """显示初始提示信息"""
        msg_frame = ttk.Frame(self.scrollable_frame)
        msg_frame.pack(expand=True, fill=tk.BOTH, pady=50)
        
        ttk.Label(msg_frame, text="📋", font=("SF Pro Display", 48)).pack()
        ttk.Label(msg_frame, text="请选择Excel文件开始配置任务", 
                 font=("SF Pro Display", 14), foreground="gray").pack(pady=10)
    
    def create_controls_section(self):
        """创建控制按钮区域"""
        controls_frame = ttk.Frame(self.main_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 左侧按钮
        left_frame = ttk.Frame(controls_frame)
        left_frame.pack(side=tk.LEFT)
        
        test_btn = ttk.Button(left_frame, text="🧪 试运行", 
                             command=self.test_run)
        test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        process_btn = ttk.Button(left_frame, text="▶ 开始处理", 
                                command=self.start_processing)
        process_btn.pack(side=tk.LEFT)
        
        # 右侧按钮
        right_frame = ttk.Frame(controls_frame)
        right_frame.pack(side=tk.RIGHT)
        
        help_btn = ttk.Button(right_frame, text="❓ 帮助", 
                             command=self.show_help)
        help_btn.pack()
    
    def create_status_section(self):
        """创建状态区域"""
        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(fill=tk.X)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='determinate')
        self.progress.pack(fill=tk.X, pady=(0, 5))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                font=("SF Pro Display", 10))
        status_label.pack()
    
    def select_file(self):
        """选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path = file_path
            filename = os.path.basename(file_path)
            self.file_var.set(f"已选择: {filename}")
            self.status_var.set(f"已选择文件: {filename}")
    
    def read_excel(self):
        """读取Excel文件"""
        if not self.file_path:
            messagebox.showerror("错误", "请先选择Excel文件")
            return
        
        try:
            self.status_var.set("正在读取Excel文件...")
            self.root.update()
            
            # 导入pandas
            import pandas as pd
            self.df = pd.read_excel(self.file_path)
            
            # 更新界面
            self.file_info_var.set(f"{len(self.df)} 行 × {len(self.df.columns)} 列")
            self.task_info_var.set("请配置需要处理的列，然后使用试运行验证")
            
            # 创建列配置
            self.create_column_configs()
            
            self.status_var.set("Excel文件读取成功")
            messagebox.showinfo("成功", f"成功读取Excel文件\n行数: {len(self.df)}\n列数: {len(self.df.columns)}")
            
        except Exception as e:
            self.status_var.set("读取失败")
            messagebox.showerror("错误", f"读取Excel文件失败:\n{str(e)}")
    
    def create_column_configs(self):
        """创建列配置界面"""
        # 清空现有内容
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # 表头
        header_frame = ttk.Frame(self.scrollable_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        headers = ["列名", "示例数据", "处理", "方式", "配置"]
        for header in headers:
            ttk.Label(header_frame, text=header, 
                     font=("SF Pro Display", 10, "bold")).pack(side=tk.LEFT, padx=10)
        
        # 创建列配置
        self.task_configs = {}
        for column in self.df.columns:
            self.create_single_column_config(column)
    
    def create_single_column_config(self, column):
        """创建单列配置"""
        config_frame = ttk.Frame(self.scrollable_frame)
        config_frame.pack(fill=tk.X, pady=2)
        
        # 列名
        ttk.Label(config_frame, text=str(column)[:15], width=15).pack(side=tk.LEFT, padx=5)
        
        # 示例数据
        first_value = str(self.df.iloc[0][column]) if len(self.df) > 0 else ""
        display_value = first_value[:20] + "..." if len(first_value) > 20 else first_value
        
        data_btn = ttk.Button(config_frame, text=display_value, width=20,
                             command=lambda: self.show_data_detail(column, first_value))
        data_btn.pack(side=tk.LEFT, padx=5)
        
        # 处理复选框
        process_var = tk.BooleanVar()
        process_check = ttk.Checkbutton(config_frame, variable=process_var)
        process_check.pack(side=tk.LEFT, padx=5)
        
        # 处理方式
        method_var = tk.StringVar()
        method_combo = ttk.Combobox(config_frame, textvariable=method_var,
                                   values=["执行算式", "按字段取值", "AI写公式", "AI自定义任务"],
                                   width=12, state="disabled")
        method_combo.pack(side=tk.LEFT, padx=5)
        
        # 配置输入
        config_var = tk.StringVar()
        config_entry = ttk.Entry(config_frame, textvariable=config_var, 
                                width=25, state="disabled")
        config_entry.pack(side=tk.LEFT, padx=5)
        
        # 绑定事件
        process_check.config(command=lambda: self.toggle_column_config(column))
        
        # 存储配置
        self.task_configs[column] = {
            'process_var': process_var,
            'method_var': method_var,
            'method_combo': method_combo,
            'config_var': config_var,
            'config_entry': config_entry
        }
    
    def toggle_column_config(self, column):
        """切换列配置状态"""
        config = self.task_configs[column]
        enabled = config['process_var'].get()
        
        state = "normal" if enabled else "disabled"
        config['method_combo'].config(state=state)
        config['config_entry'].config(state=state)
    
    def show_data_detail(self, column, content):
        """显示数据详情"""
        popup = tk.Toplevel(self.root)
        popup.title(f"列 '{column}' 的数据内容")
        popup.geometry("500x300")
        
        text_widget = scrolledtext.ScrolledText(popup)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
        
        ttk.Button(popup, text="关闭", command=popup.destroy).pack(pady=5)
    
    def test_run(self):
        """试运行"""
        if self.df is None:
            messagebox.showerror("错误", "请先读取Excel文件")
            return
        
        messagebox.showinfo("试运行", "试运行功能开发中...\n将显示第一行的处理结果")
    
    def start_processing(self):
        """开始处理"""
        if self.df is None:
            messagebox.showerror("错误", "请先读取Excel文件")
            return
        
        messagebox.showinfo("开始处理", "批量处理功能开发中...\n将处理所有数据行")
    
    def show_help(self):
        """显示帮助"""
        help_text = """Excel数据处理工具使用说明

1. 文件操作
   • 点击"选择Excel文件"选择要处理的文件
   • 点击"读取数据"加载文件内容

2. 配置任务
   • 勾选需要处理的列
   • 选择处理方式
   • 填写具体配置

3. 处理方式
   • 执行算式：数学运算，用x代表单元格值
   • 按字段取值：从JSON中提取字段
   • AI写公式：生成Excel公式
   • AI自定义任务：智能文本处理

4. 执行处理
   • 使用"试运行"验证配置
   • 点击"开始处理"执行批量处理"""
        
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """Excel数据处理工具 v2.0

专为macOS优化的Excel数据处理应用
支持多种数据处理方式和AI功能

开发环境: Python 3.9 + Tkinter
适用系统: macOS 10.14+"""
        
        messagebox.showinfo("关于", about_text)
    
    def run(self):
        """运行应用"""
        print("启动macOS版Excel应用主循环...")
        
        # 确保窗口显示
        self.root.update()
        self.root.deiconify()
        self.root.lift()
        
        # 启动主循环
        self.root.mainloop()
        print("应用已关闭")


def main():
    """主函数"""
    print("启动Excel数据处理工具 - macOS版...")
    
    try:
        app = ExcelProcessorMacOS()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
