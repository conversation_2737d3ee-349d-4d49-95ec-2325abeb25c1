('/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/PYZ-00.pyz',
 [('PyQt5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('__future__',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('_osx_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('ast',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ast.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('base64',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/base64.py',
   'PYMODULE'),
  ('bdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bdb.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('certifi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/core.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('cmd',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/cmd.py',
   'PYMODULE'),
  ('code',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/code.py',
   'PYMODULE'),
  ('codeop',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/copy.py',
   'PYMODULE'),
  ('csv',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/csv.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('dateutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('dateutil._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('dis',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dis.py',
   'PYMODULE'),
  ('doctest',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/doctest.py',
   'PYMODULE'),
  ('email',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('glob',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/glob.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hmac.py',
   'PYMODULE'),
  ('html',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('http',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/cookies.py',
   'PYMODULE'),
  ('http.server',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('idna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_common.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/resources.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ipaddress.py',
   'PYMODULE'),
  ('json',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lzma.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/char/__init__.py',
   'PYMODULE'),
  ('numpy.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/core/_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.lib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/rec/__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/strings/__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/version.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('openpyxl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_link/__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_link/external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/functions.py',
   'PYMODULE'),
  ('optparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('pandas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/__init__.py',
   'PYMODULE'),
  ('pandas._config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/localization.py',
   'PYMODULE'),
  ('pandas._libs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_typing.py',
   'PYMODULE'),
  ('pandas._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/extensions/__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/types/__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/typing/__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/numpy/__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/numpy/function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/base.py',
   'PYMODULE'),
  ('pandas.core.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/times.py',
   'PYMODULE'),
  ('pandas.core.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/errors/__init__.py',
   'PYMODULE'),
  ('pandas.io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/clipboard/__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/html.py',
   'PYMODULE'),
  ('pandas.io.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/offsets.py',
   'PYMODULE'),
  ('pandas.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/version/__init__.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('pdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pdb.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pickletools',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickletools.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/plistlib.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('pydoc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('pytz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/tzinfo.py',
   'PYMODULE'),
  ('queue',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/queue.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('random',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/random.py',
   'PYMODULE'),
  ('requests',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('requests.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.auth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests.certs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/certs.py',
   'PYMODULE'),
  ('requests.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/compat.py',
   'PYMODULE'),
  ('requests.cookies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/models.py',
   'PYMODULE'),
  ('requests.packages',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/packages.py',
   'PYMODULE'),
  ('requests.sessions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/utils.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/signal.py',
   'PYMODULE'),
  ('six',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/six.py',
   'PYMODULE'),
  ('socket',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py',
   'PYMODULE'),
  ('socketserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/dump.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ssl.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('string',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/threading.py',
   'PYMODULE'),
  ('token',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('tty',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tty.py',
   'PYMODULE'),
  ('typing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/typing.py',
   'PYMODULE'),
  ('unittest',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/__init__.py',
   'PYMODULE'),
  ('unittest._log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.case',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/case.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/loader.py',
   'PYMODULE'),
  ('unittest.main',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/main.py',
   'PYMODULE'),
  ('unittest.result',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/result.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/runner.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/signals.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/suite.py',
   'PYMODULE'),
  ('unittest.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/util.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('urllib3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('urllib3.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('uu',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uu.py',
   'PYMODULE'),
  ('uuid',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uuid.py',
   'PYMODULE'),
  ('webbrowser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('xml',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.dom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipimport.py',
   'PYMODULE')])
