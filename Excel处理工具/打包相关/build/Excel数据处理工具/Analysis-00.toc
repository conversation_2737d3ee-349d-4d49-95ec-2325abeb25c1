(['/Users/<USER>/Documents/work/cursor-workspace/excel_pyqt5_app.py'],
 ['/Users/<USER>/Documents/work/cursor-workspace'],
 [],
 [('/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.9.6 (default, Apr 30 2025, 02:07:17) \n[Clang 17.0.0 (clang-1700.0.13.5)]',
 [('pyi_rth_pyqt5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('excel_pyqt5_app',
   '/Users/<USER>/Documents/work/cursor-workspace/excel_pyqt5_app.py',
   'PYSOURCE')],
 [('multiprocessing.spawn',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('signal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/signal.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gzip.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('copy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/copy.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('random',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/random.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ssl.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('string',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/string.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('email',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('uu',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uu.py',
   'PYMODULE'),
  ('optparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('base64',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/base64.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hmac.py',
   'PYMODULE'),
  ('struct',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/struct.py',
   'PYMODULE'),
  ('socket',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._common',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_common.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('typing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/typing.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('csv',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/csv.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('token',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/token.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bz2.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('queue',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipimport.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('dis',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dis.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('ast',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ast.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('threading',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('PyQt5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('pandas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/holiday.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/wintypes.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('dateutil._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('six',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/six.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/__init__.py',
   'PYMODULE'),
  ('pandas.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/check.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/_optional.py',
   'PYMODULE'),
  ('pandas.util.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/version/__init__.py',
   'PYMODULE'),
  ('pandas.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/__init__.py',
   'PYMODULE'),
  ('pandas.core.computation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/ops.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sorting.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/categorical.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/astype.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/base.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/accessor.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/expanding.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/doc.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/shared_docs.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/objects.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/online.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/numba_.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/numba_.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/putmask.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/masked.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.apply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/base.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dataclasses.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/extension_types.py',
   'PYMODULE'),
  ('pickletools',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickletools.py',
   'PYMODULE'),
  ('doctest',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/doctest.py',
   'PYMODULE'),
  ('unittest',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/async_case.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/_log.py',
   'PYMODULE'),
  ('unittest.result',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/util.py',
   'PYMODULE'),
  ('pdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pdb.py',
   'PYMODULE'),
  ('pydoc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('http.server',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('html',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tty.py',
   'PYMODULE'),
  ('sysconfig',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_aix_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('_osx_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_osx_support.py',
   'PYMODULE'),
  ('platform',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/plistlib.py',
   'PYMODULE'),
  ('glob',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/glob.py',
   'PYMODULE'),
  ('code',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/code.py',
   'PYMODULE'),
  ('codeop',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('bdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bdb.py',
   'PYMODULE'),
  ('cmd',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/cmd.py',
   'PYMODULE'),
  ('difflib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/numpy/function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/numpy/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/integer.py',
   'PYMODULE'),
  ('pandas.io._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/inference.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_exceptions.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/missing.py',
   'PYMODULE'),
  ('pandas.core.construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/construction.py',
   'PYMODULE'),
  ('numpy.ma',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy._globals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/einsumfunc.py',
   'PYMODULE'),
  ('numpy._core._machar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_machar.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/function_base.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/memmap.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/version.py',
   'PYMODULE'),
  ('numpy._core.records',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/records.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.lib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/introspect.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_array_utils_impl.py',
   'PYMODULE'),
  ('numpy._core.umath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/managers.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/extensions/__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/take.py',
   'PYMODULE'),
  ('pandas.core.sample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/missing.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/roperator.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_decorators.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/html.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/printing.py',
   'PYMODULE'),
  ('pandas.io.formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_openpyxl.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/cell.py',
   'PYMODULE'),
  ('openpyxl.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/__init__.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/read_only.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/datetime.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/rich_text.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/base.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/text.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/nested.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/hyperlink.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/excel.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/protection.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/borders.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/fills.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/numbers.py',
   'PYMODULE'),
  ('openpyxl.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/exceptions.py',
   'PYMODULE'),
  ('openpyxl.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/numbers.py',
   'PYMODULE'),
  ('openpyxl.styles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/views.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/escape.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/container.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/core.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/defined_name.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/page.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/reference.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/image.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/units.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/table.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/differential.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/stylesheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/author.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/manifest.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/pagebreak.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/translate.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/rule.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/print_settings.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/serialisable.py',
   'PYMODULE'),
  ('openpyxl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_link/external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_link/__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/strings.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/table.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_calamine.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/types/__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/api.py',
   'PYMODULE'),
  ('uuid',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uuid.py',
   'PYMODULE'),
  ('pandas.io.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/common.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/datetimes.py',
   'PYMODULE'),
  ('pytz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/category.py',
   'PYMODULE'),
  ('pandas.core.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/common.py',
   'PYMODULE'),
  ('pandas.core.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/common.py',
   'PYMODULE'),
  ('pandas._config.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/rolling.py',
   'PYMODULE'),
  ('pandas.core.series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/accessors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/_constants.py',
   'PYMODULE'),
  ('pandas.core.resample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/clipboard/__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sql.py',
   'PYMODULE'),
  ('sqlite3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/pytables.py',
   'PYMODULE'),
  ('pandas.io.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_json.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/base.py',
   'PYMODULE'),
  ('numpy.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/strings/__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/strings.py',
   'PYMODULE'),
  ('numpy.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/core/_utils.py',
   'PYMODULE'),
  ('numpy.char',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/char/__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/rec/__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('fileinput',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_expired_attrs_2_0.py',
   'PYMODULE'),
  ('pandas._libs.window',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/__init__.py',
   'PYMODULE'),
  ('pandas._libs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/__init__.py',
   'PYMODULE'),
  ('pandas._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_tester.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/spss.py',
   'PYMODULE'),
  ('pandas.io.html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/testing.py',
   'PYMODULE'),
  ('pandas._testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_core.py',
   'PYMODULE'),
  ('pandas.io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/errors/__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/typing/__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/dtypes.py',
   'PYMODULE'),
  ('pandas.core.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/config_init.py',
   'PYMODULE'),
  ('pandas._config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/dates.py',
   'PYMODULE'),
  ('pandas.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/compressors.py',
   'PYMODULE'),
  ('__future__',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('requests',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/compat.py',
   'PYMODULE'),
  ('http.cookies',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/cookies.py',
   'PYMODULE'),
  ('requests.models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/models.py',
   'PYMODULE'),
  ('idna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.auth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ipaddress.py',
   'PYMODULE'),
  ('urllib3._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('requests.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.sessions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/utils.py',
   'PYMODULE'),
  ('requests.certs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/certs.py',
   'PYMODULE'),
  ('certifi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/core.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/resources.py',
   'PYMODULE'),
  ('requests.packages',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('urllib3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/fetch.py',
   'PYMODULE'),
  ('logging',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('json',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/scanner.py',
   'PYMODULE')],
 [('Python3.framework/Versions/3.9/Python3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Python3',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('lib-dynload/_posixshmem.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixshmem.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/pyexpat.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_scproxy.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/termios.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/binascii.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_statistics.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha512.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_random.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/math.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ssl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_hashlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha3.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_blake2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha256.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_md5.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha1.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bisect.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/unicodedata.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_contextvars.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_decimal.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/array.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/select.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_csv.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/resource.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/grp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_lzma.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bz2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/mmap.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ctypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_queue.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_pickle.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_opcode.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_heapq.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtGui.abi3.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt5/sip.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/sip.cpython-39-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtCore.abi3.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/QtCore.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtWidgets.abi3.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/QtWidgets.abi3.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/cmath.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/writers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/writers.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_asyncio.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/readline.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_multiarray_umath.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/_umath_linalg.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_elementtree.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_uuid.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sqlite3.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_multiarray_tests.cpython-39-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/md__mypyc.cpython-39-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/md.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/mtrand.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_sfc64.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_philox.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_pcg64.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_mt19937.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/bit_generator.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_generator.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_bounded_integers.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_common.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_pocketfft_umath.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/indexers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/indexers.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/aggregations.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/aggregations.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/vectorized.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/vectorized.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/tzconversion.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/tzconversion.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timezones.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timezones.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timestamps.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timestamps.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timedeltas.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timedeltas.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/strptime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/strptime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/period.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/period.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/parsing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/parsing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/offsets.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/offsets.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/np_datetime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/np_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/nattype.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/nattype.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/fields.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/fields.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/dtypes.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/dtypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/conversion.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/conversion.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/ccalendar.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/ccalendar.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/base.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/base.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslib.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslib.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/testing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/testing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sparse.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/sparse.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sas.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/sas.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/reshape.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/reshape.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/properties.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/properties.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/parsers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/parsers.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_parser.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/pandas_parser.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_datetime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/pandas_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops_dispatch.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/ops_dispatch.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/ops.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/missing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/missing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/lib.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/lib.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/json.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/json.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/join.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/join.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/interval.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/interval.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/internals.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/internals.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/indexing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/indexing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/index.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/index.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashtable.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/hashtable.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/hashing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/groupby.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/groupby.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/byteswap.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/byteswap.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/arrays.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/arrays.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/algos.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/algos.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_json.cpython-39-darwin.so',
   'EXTENSION'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'BINARY')],
 [],
 [],
 [('PyQt5/Qt5/translations/qt_help_nl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ar.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_TW.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_uk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_CN.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_de.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ar.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_PT.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_da.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_en.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_he.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_it.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gd.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ja.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_uk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_de.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_es.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_cs.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fa.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nn.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ru.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nn.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ko.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_es.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ru.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_gd.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fi.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_da.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_lv.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ja.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lv.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_de.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ca.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ja.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_bg.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fa.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hu.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_he.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fi.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ko.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ar.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hu.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_it.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_bg.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hu.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lt.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_lt.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_en.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_gl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_fr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_BR.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ko.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_es.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_tr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sv.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_sv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ca.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nn.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_bg.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_cs.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ca.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_en.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_it.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_tr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_tr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_cs.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_da.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_uk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_sk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ru.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ru.qm',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas/io/formats/templates/latex.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_table.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_longtable.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex_longtable.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_table.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/string.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/string.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_style.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html_style.tpl',
   'DATA'),
  ('pytz/zoneinfo/Europe/Moscow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Moscow',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Palmer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Palmer',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Easter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Easter',
   'DATA'),
  ('pytz/zoneinfo/Africa/Malabo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Malabo',
   'DATA'),
  ('pytz/zoneinfo/America/Godthab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Godthab',
   'DATA'),
  ('pytz/zoneinfo/Europe/Podgorica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Podgorica',
   'DATA'),
  ('pytz/zoneinfo/Asia/Harbin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Harbin',
   'DATA'),
  ('pytz/zoneinfo/America/Jujuy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kampala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kampala',
   'DATA'),
  ('pytz/zoneinfo/Africa/Cairo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Cairo',
   'DATA'),
  ('pytz/zoneinfo/GB',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GB',
   'DATA'),
  ('pytz/zoneinfo/Europe/Warsaw',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Warsaw',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Niue',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Niue',
   'DATA'),
  ('pytz/zoneinfo/Etc/Zulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Mexico/General',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/General',
   'DATA'),
  ('pytz/zoneinfo/America/Danmarkshavn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Danmarkshavn',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sarajevo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Sarajevo',
   'DATA'),
  ('pytz/zoneinfo/PRC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/PRC',
   'DATA'),
  ('pytz/zoneinfo/Etc/UCT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/UCT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Barnaul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Barnaul',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lord_Howe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Lord_Howe',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Mawson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Mawson',
   'DATA'),
  ('pytz/zoneinfo/America/Panama',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Panama',
   'DATA'),
  ('pytz/zoneinfo/America/Cuiaba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cuiaba',
   'DATA'),
  ('pytz/zoneinfo/America/El_Salvador',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/El_Salvador',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kirov',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kirov',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pitcairn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pitcairn',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Indianapolis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Australia/Queensland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Queensland',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bangkok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bangkok',
   'DATA'),
  ('pytz/zoneinfo/America/Louisville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Louisville',
   'DATA'),
  ('pytz/zoneinfo/EET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EET',
   'DATA'),
  ('pytz/zoneinfo/Turkey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Turkey',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vevay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Vevay',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chuuk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Chuuk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pago_Pago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pago_Pago',
   'DATA'),
  ('pytz/zoneinfo/America/Santiago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santiago',
   'DATA'),
  ('pytz/zoneinfo/America/Detroit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Detroit',
   'DATA'),
  ('pytz/zoneinfo/America/Creston',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Creston',
   'DATA'),
  ('pytz/zoneinfo/Australia/Yancowinna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Yancowinna',
   'DATA'),
  ('pytz/zoneinfo/Canada/Atlantic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Atlantic',
   'DATA'),
  ('pytz/zoneinfo/America/Edmonton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Edmonton',
   'DATA'),
  ('pytz/zoneinfo/Europe/Lisbon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Lisbon',
   'DATA'),
  ('pytz/zoneinfo/America/Boise',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Boise',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baku',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Baku',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Luis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/San_Luis',
   'DATA'),
  ('pytz/zoneinfo/EST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EST',
   'DATA'),
  ('pytz/zoneinfo/Arctic/Longyearbyen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Arctic/Longyearbyen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bahrain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bahrain',
   'DATA'),
  ('pytz/zoneinfo/America/Mazatlan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mazatlan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Urumqi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Urumqi',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kiritimati',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kiritimati',
   'DATA'),
  ('pytz/zoneinfo/GMT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT',
   'DATA'),
  ('pytz/zoneinfo/Europe/Malta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Malta',
   'DATA'),
  ('pytz/zoneinfo/America/Atka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Atka',
   'DATA'),
  ('pytz/zoneinfo/America/Guatemala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guatemala',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-6',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-6',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Yap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Yap',
   'DATA'),
  ('pytz/zoneinfo/America/Vancouver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Vancouver',
   'DATA'),
  ('pytz/zoneinfo/America/Lima',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Lima',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Winamac',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Winamac',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faroe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Faroe',
   'DATA'),
  ('pytz/zoneinfo/US/East-Indiana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/East-Indiana',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Mendoza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Asia/Istanbul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Calcutta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Calcutta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Budapest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Budapest',
   'DATA'),
  ('pytz/zoneinfo/Africa/Luanda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Luanda',
   'DATA'),
  ('pytz/zoneinfo/America/Santarem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santarem',
   'DATA'),
  ('pytz/zoneinfo/Indian/Kerguelen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Kerguelen',
   'DATA'),
  ('pytz/zoneinfo/America/Campo_Grande',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Campo_Grande',
   'DATA'),
  ('pytz/zoneinfo/MET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MET',
   'DATA'),
  ('pytz/zoneinfo/America/Nuuk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nuuk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tarawa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tarawa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bucharest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Bucharest',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/South_Georgia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/South_Georgia',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-7',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-7',
   'DATA'),
  ('pytz/zoneinfo/Europe/Riga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Riga',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulan_Bator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ulan_Bator',
   'DATA'),
  ('pytz/zoneinfo/America/Goose_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Goose_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zaporozhye',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zaporozhye',
   'DATA'),
  ('pytz/zoneinfo/Asia/Almaty',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Almaty',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yakutsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yakutsk',
   'DATA'),
  ('pytz/zoneinfo/America/Jamaica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Indian/Christmas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Christmas',
   'DATA'),
  ('pytz/zoneinfo/America/Nassau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nassau',
   'DATA'),
  ('pytz/zoneinfo/America/Managua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Managua',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Nauru',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Nauru',
   'DATA'),
  ('pytz/zoneinfo/America/Merida',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Merida',
   'DATA'),
  ('pytz/zoneinfo/Europe/Istanbul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Africa/Abidjan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Abidjan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chongqing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chongqing',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+1',
   'DATA'),
  ('pytz/zoneinfo/Asia/Singapore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vatican',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vatican',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dili',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dili',
   'DATA'),
  ('pytz/zoneinfo/Asia/Oral',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Oral',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nairobi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Nairobi',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kiev',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kiev',
   'DATA'),
  ('pytz/zoneinfo/Europe/Rome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Rome',
   'DATA'),
  ('pytz/zoneinfo/America/Rio_Branco',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rio_Branco',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+3',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT',
   'DATA'),
  ('pytz/zoneinfo/Singapore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Truk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Truk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Mariehamn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Mariehamn',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tbilisi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tbilisi',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-11',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Buenos_Aires',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tahiti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tahiti',
   'DATA'),
  ('pytz/zoneinfo/Brazil/West',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/West',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ljubljana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Ljubljana',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qyzylorda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qyzylorda',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pontianak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Pontianak',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dar_es_Salaam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Dar_es_Salaam',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ndjamena',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ndjamena',
   'DATA'),
  ('pytz/zoneinfo/America/Halifax',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Halifax',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Canary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Canary',
   'DATA'),
  ('pytz/zoneinfo/America/Phoenix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Phoenix',
   'DATA'),
  ('pytz/zoneinfo/Europe/Helsinki',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Helsinki',
   'DATA'),
  ('pytz/zoneinfo/Asia/Sakhalin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Sakhalin',
   'DATA'),
  ('pytz/zoneinfo/America/Miquelon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Miquelon',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fakaofo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Fakaofo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Omsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Omsk',
   'DATA'),
  ('pytz/zoneinfo/America/Guayaquil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guayaquil',
   'DATA'),
  ('pytz/zoneinfo/Australia/Darwin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Darwin',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aqtau',
   'DATA'),
  ('pytz/zoneinfo/America/Atikokan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Atikokan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Dublin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Dublin',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaSur',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/BajaSur',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-4',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-4',
   'DATA'),
  ('pytz/zoneinfo/Indian/Cocos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Cocos',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Marquesas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Marquesas',
   'DATA'),
  ('pytz/zoneinfo/Australia/Sydney',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Sydney',
   'DATA'),
  ('pytz/zoneinfo/America/Cayenne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cayenne',
   'DATA'),
  ('pytz/zoneinfo/Australia/Canberra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Canberra',
   'DATA'),
  ('pytz/zoneinfo/Africa/Niamey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Niamey',
   'DATA'),
  ('pytz/zoneinfo/America/Caracas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Caracas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yerevan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yerevan',
   'DATA'),
  ('pytz/zoneinfo/America/Araguaina',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Araguaina',
   'DATA'),
  ('pytz/zoneinfo/America/Nipigon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nipigon',
   'DATA'),
  ('pytz/zoneinfo/America/St_Lucia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Lucia',
   'DATA'),
  ('pytz/zoneinfo/Australia/Hobart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Hobart',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-13',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-13',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mahe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mahe',
   'DATA'),
  ('pytz/zoneinfo/Africa/Johannesburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Johannesburg',
   'DATA'),
  ('pytz/zoneinfo/Australia/Broken_Hill',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Broken_Hill',
   'DATA'),
  ('pytz/zoneinfo/Indian/Reunion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Reunion',
   'DATA'),
  ('pytz/zoneinfo/America/Lower_Princes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Lower_Princes',
   'DATA'),
  ('pytz/zoneinfo/Greenwich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kwajalein',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Asia/Katmandu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Katmandu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Minsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Minsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Monrovia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Monrovia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lubumbashi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lubumbashi',
   'DATA'),
  ('pytz/zoneinfo/Europe/Volgograd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Volgograd',
   'DATA'),
  ('pytz/zoneinfo/tzdata.zi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/tzdata.zi',
   'DATA'),
  ('pytz/zoneinfo/Australia/Victoria',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Victoria',
   'DATA'),
  ('pytz/zoneinfo/America/Aruba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Aruba',
   'DATA'),
  ('pytz/zoneinfo/America/St_Johns',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Johns',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-14',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-14',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-2',
   'DATA'),
  ('pytz/zoneinfo/Europe/Athens',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Athens',
   'DATA'),
  ('pytz/zoneinfo/America/Guyana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guyana',
   'DATA'),
  ('pytz/zoneinfo/America/Metlakatla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Metlakatla',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qostanay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qostanay',
   'DATA'),
  ('pytz/zoneinfo/America/Rankin_Inlet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rankin_Inlet',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yekaterinburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yekaterinburg',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+8',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+8',
   'DATA'),
  ('pytz/zoneinfo/Canada/Mountain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Mountain',
   'DATA'),
  ('pytz/zoneinfo/zone.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zone.tab',
   'DATA'),
  ('pytz/zoneinfo/America/Cayman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cayman',
   'DATA'),
  ('pytz/zoneinfo/Europe/Chisinau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Chisinau',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuching',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuching',
   'DATA'),
  ('pytz/zoneinfo/Iran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Iran',
   'DATA'),
  ('pytz/zoneinfo/CST6CDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/CST6CDT',
   'DATA'),
  ('pytz/zoneinfo/America/Grand_Turk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Grand_Turk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Shanghai',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Shanghai',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimbu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Thimbu',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+10',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+10',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tripoli',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Tripoli',
   'DATA'),
  ('pytz/zoneinfo/America/Nome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nome',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Velho',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Porto_Velho',
   'DATA'),
  ('pytz/zoneinfo/America/Hermosillo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Hermosillo',
   'DATA'),
  ('pytz/zoneinfo/America/Eirunepe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Eirunepe',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/New_Salem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/New_Salem',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pyongyang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Pyongyang',
   'DATA'),
  ('pytz/zoneinfo/Europe/Monaco',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Monaco',
   'DATA'),
  ('pytz/zoneinfo/US/Mountain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/St_Helena',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/St_Helena',
   'DATA'),
  ('pytz/zoneinfo/Chile/EasterIsland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Chile/EasterIsland',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Tell_City',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Tell_City',
   'DATA'),
  ('pytz/zoneinfo/Asia/Gaza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Gaza',
   'DATA'),
  ('pytz/zoneinfo/Portugal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Portugal',
   'DATA'),
  ('pytz/zoneinfo/America/Sitka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Sitka',
   'DATA'),
  ('pytz/zoneinfo/Africa/Porto-Novo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Porto-Novo',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Petersburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Petersburg',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/McMurdo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/McMurdo',
   'DATA'),
  ('pytz/zoneinfo/America/Belem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Belem',
   'DATA'),
  ('pytz/zoneinfo/Africa/Juba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Juba',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Madeira',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Madeira',
   'DATA'),
  ('pytz/zoneinfo/America/Catamarca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dhaka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dhaka',
   'DATA'),
  ('pytz/zoneinfo/America/Monterrey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Monterrey',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimphu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Thimphu',
   'DATA'),
  ('pytz/zoneinfo/iso3166.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/iso3166.tab',
   'DATA'),
  ('pytz/zoneinfo/Europe/Amsterdam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Amsterdam',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+2',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Ushuaia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Ushuaia',
   'DATA'),
  ('pytz/zoneinfo/America/Moncton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Moncton',
   'DATA'),
  ('pytz/zoneinfo/Australia/Melbourne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Melbourne',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jerusalem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jerusalem',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pohnpei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pohnpei',
   'DATA'),
  ('pytz/zoneinfo/America/Kralendijk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kralendijk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Ponape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Ponape',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kanton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kanton',
   'DATA'),
  ('pytz/zoneinfo/Etc/Universal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Universal',
   'DATA'),
  ('pytz/zoneinfo/leapseconds',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/leapseconds',
   'DATA'),
  ('pytz/zoneinfo/Asia/Nicosia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Indian/Antananarivo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Antananarivo',
   'DATA'),
  ('pytz/zoneinfo/GMT+0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/America/Asuncion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Asuncion',
   'DATA'),
  ('pytz/zoneinfo/America/Shiprock',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Shiprock',
   'DATA'),
  ('pytz/zoneinfo/America/Whitehorse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Whitehorse',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Rothera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Rothera',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Palau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Palau',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Syowa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Syowa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kaliningrad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kaliningrad',
   'DATA'),
  ('pytz/zoneinfo/America/Port_of_Spain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Port_of_Spain',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+5',
   'DATA'),
  ('pytz/zoneinfo/America/Denver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Denver',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ulyanovsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Ulyanovsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Addis_Ababa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Addis_Ababa',
   'DATA'),
  ('pytz/zoneinfo/America/Ensenada',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ensenada',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maputo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Maputo',
   'DATA'),
  ('pytz/zoneinfo/America/Grenada',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Grenada',
   'DATA'),
  ('pytz/zoneinfo/America/Anguilla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Anguilla',
   'DATA'),
  ('pytz/zoneinfo/America/Tijuana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tijuana',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kinshasa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kinshasa',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tongatapu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tongatapu',
   'DATA'),
  ('pytz/zoneinfo/America/Bogota',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bogota',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Samoa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Troll',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Troll',
   'DATA'),
  ('pytz/zoneinfo/Asia/Muscat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Muscat',
   'DATA'),
  ('pytz/zoneinfo/Cuba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Cuba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Famagusta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Famagusta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vienna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vienna',
   'DATA'),
  ('pytz/zoneinfo/Europe/Simferopol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Simferopol',
   'DATA'),
  ('pytz/zoneinfo/America/Virgin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Virgin',
   'DATA'),
  ('pytz/zoneinfo/America/Pangnirtung',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Pangnirtung',
   'DATA'),
  ('pytz/zoneinfo/Israel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Israel',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashgabat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ashgabat',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zagreb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zagreb',
   'DATA'),
  ('pytz/zoneinfo/Europe/Astrakhan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Astrakhan',
   'DATA'),
  ('pytz/zoneinfo/America/Indianapolis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kyiv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kyiv',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wallis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Wallis',
   'DATA'),
  ('pytz/zoneinfo/Universal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/Montevideo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montevideo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+6',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+6',
   'DATA'),
  ('pytz/zoneinfo/Africa/Khartoum',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Khartoum',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zurich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zurich',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kabul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kabul',
   'DATA'),
  ('pytz/zoneinfo/America/Rosario',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rosario',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tokyo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tokyo',
   'DATA'),
  ('pytz/zoneinfo/America/Boa_Vista',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Boa_Vista',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tomsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tomsk',
   'DATA'),
  ('pytz/zoneinfo/Zulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Zulu',
   'DATA'),
  ('pytz/zoneinfo/America/Tortola',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tortola',
   'DATA'),
  ('pytz/zoneinfo/Africa/Conakry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Conakry',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dubai',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dubai',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bamako',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bamako',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulaanbaatar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ulaanbaatar',
   'DATA'),
  ('pytz/zoneinfo/Factory',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Factory',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chita',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chita',
   'DATA'),
  ('pytz/zoneinfo/Asia/Makassar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Makassar',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-9',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-9',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baghdad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Baghdad',
   'DATA'),
  ('pytz/zoneinfo/America/Tegucigalpa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tegucigalpa',
   'DATA'),
  ('pytz/zoneinfo/Poland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Poland',
   'DATA'),
  ('pytz/zoneinfo/ROK',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/ROK',
   'DATA'),
  ('pytz/zoneinfo/Europe/Oslo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Oslo',
   'DATA'),
  ('pytz/zoneinfo/US/Eastern',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Eastern',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Louisville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kentucky/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/South_Pole',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/South_Pole',
   'DATA'),
  ('pytz/zoneinfo/US/Alaska',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Alaska',
   'DATA'),
  ('pytz/zoneinfo/Europe/Jersey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Jersey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Berlin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Berlin',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lusaka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lusaka',
   'DATA'),
  ('pytz/zoneinfo/Africa/Freetown',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Freetown',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bujumbura',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bujumbura',
   'DATA'),
  ('pytz/zoneinfo/Etc/Greenwich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vilnius',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vilnius',
   'DATA'),
  ('pytz/zoneinfo/Jamaica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ujung_Pandang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ujung_Pandang',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuala_Lumpur',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuala_Lumpur',
   'DATA'),
  ('pytz/zoneinfo/Europe/Busingen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Busingen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuwait',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuwait',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Rarotonga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Rarotonga',
   'DATA'),
  ('pytz/zoneinfo/America/Adak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Adak',
   'DATA'),
  ('pytz/zoneinfo/Europe/Brussels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Brussels',
   'DATA'),
  ('pytz/zoneinfo/America/Puerto_Rico',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Puerto_Rico',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Apia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Apia',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kosrae',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kosrae',
   'DATA'),
  ('pytz/zoneinfo/Brazil/Acre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/Acre',
   'DATA'),
  ('pytz/zoneinfo/America/Juneau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Juneau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Banjul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Banjul',
   'DATA'),
  ('pytz/zoneinfo/America/Antigua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Antigua',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vincennes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Vincennes',
   'DATA'),
  ('pytz/zoneinfo/WET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/WET',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tunis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Tunis',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ouagadougou',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ouagadougou',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tiraspol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tiraspol',
   'DATA'),
  ('pytz/zoneinfo/Asia/Krasnoyarsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Krasnoyarsk',
   'DATA'),
  ('pytz/zoneinfo/Australia/Tasmania',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Tasmania',
   'DATA'),
  ('pytz/zoneinfo/Australia/Brisbane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Brisbane',
   'DATA'),
  ('pytz/zoneinfo/Europe/Gibraltar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Gibraltar',
   'DATA'),
  ('pytz/zoneinfo/CET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/CET',
   'DATA'),
  ('pytz/zoneinfo/US/Samoa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novosibirsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Novosibirsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tallinn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tallinn',
   'DATA'),
  ('pytz/zoneinfo/Australia/Currie',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Currie',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tashkent',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tashkent',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Wayne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fort_Wayne',
   'DATA'),
  ('pytz/zoneinfo/W-SU',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/W-SU',
   'DATA'),
  ('pytz/zoneinfo/America/Iqaluit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Iqaluit',
   'DATA'),
  ('pytz/zoneinfo/ROC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/ROC',
   'DATA'),
  ('pytz/zoneinfo/Asia/Seoul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Seoul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Phnom_Penh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Phnom_Penh',
   'DATA'),
  ('pytz/zoneinfo/America/New_York',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/New_York',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Jan_Mayen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Jan_Mayen',
   'DATA'),
  ('pytz/zoneinfo/Africa/Brazzaville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Brazzaville',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Nelson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fort_Nelson',
   'DATA'),
  ('pytz/zoneinfo/Canada/Saskatchewan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Saskatchewan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chatham',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Chatham',
   'DATA'),
  ('pytz/zoneinfo/Europe/San_Marino',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/San_Marino',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bangui',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bangui',
   'DATA'),
  ('pytz/zoneinfo/Libya',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Libya',
   'DATA'),
  ('pytz/zoneinfo/America/Knox_IN',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Knox_IN',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mauritius',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mauritius',
   'DATA'),
  ('pytz/zoneinfo/Africa/Blantyre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Blantyre',
   'DATA'),
  ('pytz/zoneinfo/Africa/Libreville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Libreville',
   'DATA'),
  ('pytz/zoneinfo/America/Havana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Havana',
   'DATA'),
  ('pytz/zoneinfo/Iceland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Iceland',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dawson',
   'DATA'),
  ('pytz/zoneinfo/PST8PDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/PST8PDT',
   'DATA'),
  ('pytz/zoneinfo/America/Barbados',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Barbados',
   'DATA'),
  ('pytz/zoneinfo/Asia/Taipei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Taipei',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hebron',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hebron',
   'DATA'),
  ('pytz/zoneinfo/Asia/Samarkand',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Samarkand',
   'DATA'),
  ('pytz/zoneinfo/America/Menominee',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Menominee',
   'DATA'),
  ('pytz/zoneinfo/America/Montreal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montreal',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Enderbury',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Enderbury',
   'DATA'),
  ('pytz/zoneinfo/America/Santa_Isabel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santa_Isabel',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtobe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aqtobe',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dakar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Dakar',
   'DATA'),
  ('pytz/zoneinfo/Australia/Eucla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Eucla',
   'DATA'),
  ('pytz/zoneinfo/America/Cambridge_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cambridge_Bay',
   'DATA'),
  ('pytz/zoneinfo/Africa/Harare',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Harare',
   'DATA'),
  ('pytz/zoneinfo/America/Chihuahua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Chihuahua',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bissau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bissau',
   'DATA'),
  ('pytz/zoneinfo/America/Costa_Rica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Costa_Rica',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/ComodRivadavia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/ComodRivadavia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macao',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Macao',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-5',
   'DATA'),
  ('pytz/zoneinfo/Europe/Paris',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Paris',
   'DATA'),
  ('pytz/zoneinfo/America/Punta_Arenas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Punta_Arenas',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Efate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Efate',
   'DATA'),
  ('pytz/zoneinfo/America/Mexico_City',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mexico_City',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novokuznetsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Novokuznetsk',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Macquarie',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Macquarie',
   'DATA'),
  ('pytz/zoneinfo/America/Santo_Domingo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santo_Domingo',
   'DATA'),
  ('pytz/zoneinfo/Europe/London',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/London',
   'DATA'),
  ('pytz/zoneinfo/Canada/Yukon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Yukon',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia_Banderas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bahia_Banderas',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fiji',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Fiji',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Galapagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Galapagos',
   'DATA'),
  ('pytz/zoneinfo/Indian/Maldives',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Maldives',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-8',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-8',
   'DATA'),
  ('pytz/zoneinfo/HST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/HST',
   'DATA'),
  ('pytz/zoneinfo/Asia/Saigon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Saigon',
   'DATA'),
  ('pytz/zoneinfo/Africa/El_Aaiun',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/El_Aaiun',
   'DATA'),
  ('pytz/zoneinfo/America/Buenos_Aires',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bahia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mogadishu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Mogadishu',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Cordoba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/America/Ojinaga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ojinaga',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ust-Nera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ust-Nera',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Funafuti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Funafuti',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bishkek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bishkek',
   'DATA'),
  ('pytz/zoneinfo/America/Matamoros',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Matamoros',
   'DATA'),
  ('pytz/zoneinfo/America/Resolute',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Resolute',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-1',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Knox',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Knox',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vientiane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Vientiane',
   'DATA'),
  ('pytz/zoneinfo/America/Glace_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Glace_Bay',
   'DATA'),
  ('pytz/zoneinfo/Asia/Beirut',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Beirut',
   'DATA'),
  ('pytz/zoneinfo/America/Sao_Paulo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Sao_Paulo',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Johnston',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Johnston',
   'DATA'),
  ('pytz/zoneinfo/Australia/Adelaide',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Adelaide',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Bougainville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Bougainville',
   'DATA'),
  ('pytz/zoneinfo/GMT-0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/America/Yakutat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Yakutat',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lindeman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Lindeman',
   'DATA'),
  ('pytz/zoneinfo/MST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MST',
   'DATA'),
  ('pytz/zoneinfo/America/St_Barthelemy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Barthelemy',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Azores',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Azores',
   'DATA'),
  ('pytz/zoneinfo/America/Los_Angeles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Los_Angeles',
   'DATA'),
  ('pytz/zoneinfo/Australia/NSW',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/NSW',
   'DATA'),
  ('pytz/zoneinfo/America/Ciudad_Juarez',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ciudad_Juarez',
   'DATA'),
  ('pytz/zoneinfo/Africa/Casablanca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Casablanca',
   'DATA'),
  ('pytz/zoneinfo/Europe/Madrid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Madrid',
   'DATA'),
  ('pytz/zoneinfo/MST7MDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MST7MDT',
   'DATA'),
  ('pytz/zoneinfo/America/Fortaleza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fortaleza',
   'DATA'),
  ('pytz/zoneinfo/Asia/Brunei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Brunei',
   'DATA'),
  ('pytz/zoneinfo/Europe/Prague',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Prague',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Acre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Porto_Acre',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Reykjavik',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Reykjavik',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson_Creek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dawson_Creek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kathmandu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kathmandu',
   'DATA'),
  ('pytz/zoneinfo/America/Curacao',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Curacao',
   'DATA'),
  ('pytz/zoneinfo/America/Cancun',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cancun',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hovd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hovd',
   'DATA'),
  ('pytz/zoneinfo/Africa/Windhoek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Windhoek',
   'DATA'),
  ('pytz/zoneinfo/Europe/Copenhagen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Copenhagen',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mayotte',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mayotte',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ceuta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ceuta',
   'DATA'),
  ('pytz/zoneinfo/America/Chicago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Chicago',
   'DATA'),
  ('pytz/zoneinfo/Asia/Anadyr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Anadyr',
   'DATA'),
  ('pytz/zoneinfo/Australia/LHI',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/LHI',
   'DATA'),
  ('pytz/zoneinfo/Brazil/DeNoronha',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/DeNoronha',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kolkata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kolkata',
   'DATA'),
  ('pytz/zoneinfo/Asia/Amman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Amman',
   'DATA'),
  ('pytz/zoneinfo/America/St_Vincent',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Vincent',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Majuro',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Majuro',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Auckland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Auckland',
   'DATA'),
  ('pytz/zoneinfo/Canada/Pacific',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Dominica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dominica',
   'DATA'),
  ('pytz/zoneinfo/US/Hawaii',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Hawaii',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Honolulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Honolulu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Guernsey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Guernsey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Stockholm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Stockholm',
   'DATA'),
  ('pytz/zoneinfo/US/Central',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Central',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+11',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tirane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tirane',
   'DATA'),
  ('pytz/zoneinfo/America/Coyhaique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Coyhaique',
   'DATA'),
  ('pytz/zoneinfo/Etc/UTC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/UTC',
   'DATA'),
  ('pytz/zoneinfo/UCT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/UCT',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+9',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+9',
   'DATA'),
  ('pytz/zoneinfo/zonenow.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zonenow.tab',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Center',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/Center',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faeroe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Faeroe',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vaduz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vaduz',
   'DATA'),
  ('pytz/zoneinfo/Asia/Irkutsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Irkutsk',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Stanley',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Stanley',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belgrade',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Belgrade',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kamchatka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kamchatka',
   'DATA'),
  ('pytz/zoneinfo/America/Toronto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Toronto',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Jujuy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nouakchott',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Nouakchott',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dushanbe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dushanbe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Manila',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Manila',
   'DATA'),
  ('pytz/zoneinfo/America/Regina',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Regina',
   'DATA'),
  ('pytz/zoneinfo/EST5EDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EST5EDT',
   'DATA'),
  ('pytz/zoneinfo/America/St_Kitts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Kitts',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmara',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Asmara',
   'DATA'),
  ('pytz/zoneinfo/Europe/Saratov',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Saratov',
   'DATA'),
  ('pytz/zoneinfo/UTC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/UTC',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Midway',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Midway',
   'DATA'),
  ('pytz/zoneinfo/Europe/Luxembourg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Luxembourg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chungking',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chungking',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Bermuda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Bermuda',
   'DATA'),
  ('pytz/zoneinfo/America/Scoresbysund',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Scoresbysund',
   'DATA'),
  ('pytz/zoneinfo/America/Noronha',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Noronha',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-3',
   'DATA'),
  ('pytz/zoneinfo/GB-Eire',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GB-Eire',
   'DATA'),
  ('pytz/zoneinfo/Asia/Damascus',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Damascus',
   'DATA'),
  ('pytz/zoneinfo/GMT0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qatar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qatar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ho_Chi_Minh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ho_Chi_Minh',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Saipan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Saipan',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Tucuman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Tucuman',
   'DATA'),
  ('pytz/zoneinfo/America/Blanc-Sablon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Blanc-Sablon',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Noumea',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Noumea',
   'DATA'),
  ('pytz/zoneinfo/Australia/South',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/South',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Gambier',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Gambier',
   'DATA'),
  ('pytz/zoneinfo/US/Aleutian',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Aleutian',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lome',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-12',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-12',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lagos',
   'DATA'),
  ('pytz/zoneinfo/America/Marigot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Marigot',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maseru',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Maseru',
   'DATA'),
  ('pytz/zoneinfo/Chile/Continental',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Chile/Continental',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Australia/West',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/West',
   'DATA'),
  ('pytz/zoneinfo/Europe/Isle_of_Man',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Isle_of_Man',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Norfolk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Norfolk',
   'DATA'),
  ('pytz/zoneinfo/US/Indiana-Starke',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Indiana-Starke',
   'DATA'),
  ('pytz/zoneinfo/Europe/Andorra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Andorra',
   'DATA'),
  ('pytz/zoneinfo/America/Thule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Thule',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hong_Kong',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hong_Kong',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tehran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tehran',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vladivostok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Vladivostok',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/DumontDUrville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/DumontDUrville',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Marengo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Marengo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mbabane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Mbabane',
   'DATA'),
  ('pytz/zoneinfo/Europe/Uzhgorod',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Uzhgorod',
   'DATA'),
  ('pytz/zoneinfo/Asia/Karachi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Karachi',
   'DATA'),
  ('pytz/zoneinfo/Australia/North',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/North',
   'DATA'),
  ('pytz/zoneinfo/America/Manaus',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Manaus',
   'DATA'),
  ('pytz/zoneinfo/Africa/Accra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Accra',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jakarta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jakarta',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Salta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Salta',
   'DATA'),
  ('pytz/zoneinfo/Africa/Gaborone',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Gaborone',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wake',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Wake',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Catamarca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Asia/Colombo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Colombo',
   'DATA'),
  ('pytz/zoneinfo/America/Cordoba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/America/Martinique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Martinique',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-10',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-10',
   'DATA'),
  ('pytz/zoneinfo/America/Anchorage',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Anchorage',
   'DATA'),
  ('pytz/zoneinfo/Europe/Nicosia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Cape_Verde',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Cape_Verde',
   'DATA'),
  ('pytz/zoneinfo/Australia/Perth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Perth',
   'DATA'),
  ('pytz/zoneinfo/zone1970.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zone1970.tab',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guadalcanal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Guadalcanal',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaNorte',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/BajaNorte',
   'DATA'),
  ('pytz/zoneinfo/America/Guadeloupe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guadeloupe',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Juan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/San_Juan',
   'DATA'),
  ('pytz/zoneinfo/Indian/Comoro',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Comoro',
   'DATA'),
  ('pytz/zoneinfo/America/Coral_Harbour',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Coral_Harbour',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Monticello',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kentucky/Monticello',
   'DATA'),
  ('pytz/zoneinfo/America/Inuvik',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Inuvik',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Vostok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Vostok',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yangon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yangon',
   'DATA'),
  ('pytz/zoneinfo/Africa/Timbuktu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Timbuktu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kashgar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kashgar',
   'DATA'),
  ('pytz/zoneinfo/America/La_Paz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/La_Paz',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jayapura',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jayapura',
   'DATA'),
  ('pytz/zoneinfo/Australia/ACT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/ACT',
   'DATA'),
  ('pytz/zoneinfo/US/Arizona',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Arizona',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aden',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aden',
   'DATA'),
  ('pytz/zoneinfo/America/Paramaribo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Paramaribo',
   'DATA'),
  ('pytz/zoneinfo/America/Maceio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Maceio',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Beulah',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/Beulah',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Davis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Davis',
   'DATA'),
  ('pytz/zoneinfo/Asia/Rangoon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Rangoon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Skopje',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Skopje',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bratislava',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Bratislava',
   'DATA'),
  ('pytz/zoneinfo/America/Swift_Current',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Swift_Current',
   'DATA'),
  ('pytz/zoneinfo/America/Mendoza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Casey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Casey',
   'DATA'),
  ('pytz/zoneinfo/Kwajalein',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Asmera',
   'DATA'),
  ('pytz/zoneinfo/America/St_Thomas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Thomas',
   'DATA'),
  ('pytz/zoneinfo/US/Pacific',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Pacific',
   'DATA'),
  ('pytz/zoneinfo/Canada/Central',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Central',
   'DATA'),
  ('pytz/zoneinfo/Eire',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Eire',
   'DATA'),
  ('pytz/zoneinfo/NZ-CHAT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/NZ-CHAT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Riyadh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Riyadh',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/La_Rioja',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/La_Rioja',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Guam',
   'DATA'),
  ('pytz/zoneinfo/America/Port-au-Prince',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Port-au-Prince',
   'DATA'),
  ('pytz/zoneinfo/Asia/Choibalsan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Choibalsan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dacca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dacca',
   'DATA'),
  ('pytz/zoneinfo/America/Rainy_River',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rainy_River',
   'DATA'),
  ('pytz/zoneinfo/America/Belize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Belize',
   'DATA'),
  ('pytz/zoneinfo/America/Yellowknife',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Yellowknife',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kigali',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kigali',
   'DATA'),
  ('pytz/zoneinfo/US/Michigan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Michigan',
   'DATA'),
  ('pytz/zoneinfo/Canada/Eastern',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Macau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Algiers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Algiers',
   'DATA'),
  ('pytz/zoneinfo/Africa/Sao_Tome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Sao_Tome',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Port_Moresby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Port_Moresby',
   'DATA'),
  ('pytz/zoneinfo/Brazil/East',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/East',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Atyrau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Atyrau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Djibouti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Djibouti',
   'DATA'),
  ('pytz/zoneinfo/America/Winnipeg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Winnipeg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tel_Aviv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tel_Aviv',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+4',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+4',
   'DATA'),
  ('pytz/zoneinfo/Canada/Newfoundland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Newfoundland',
   'DATA'),
  ('pytz/zoneinfo/America/Montserrat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montserrat',
   'DATA'),
  ('pytz/zoneinfo/Asia/Magadan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Magadan',
   'DATA'),
  ('pytz/zoneinfo/Hongkong',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Hongkong',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+12',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+12',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashkhabad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ashkhabad',
   'DATA'),
  ('pytz/zoneinfo/Egypt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Egypt',
   'DATA'),
  ('pytz/zoneinfo/America/Thunder_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Thunder_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sofia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Sofia',
   'DATA'),
  ('pytz/zoneinfo/NZ',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/NZ',
   'DATA'),
  ('pytz/zoneinfo/Europe/Samara',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Samara',
   'DATA'),
  ('pytz/zoneinfo/America/Recife',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Recife',
   'DATA'),
  ('pytz/zoneinfo/Japan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Japan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+7',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+7',
   'DATA'),
  ('pytz/zoneinfo/Navajo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Navajo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Khandyga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Khandyga',
   'DATA'),
  ('pytz/zoneinfo/Asia/Srednekolymsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Srednekolymsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Douala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Douala',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belfast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Belfast',
   'DATA'),
  ('pytz/zoneinfo/Indian/Chagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Chagos',
   'DATA'),
  ('certifi/cacert.pem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/cacert.pem',
   'DATA'),
  ('certifi/py.typed',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/py.typed',
   'DATA'),
  ('Python3', 'Python3.framework/Versions/3.9/Python3', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/base_library.zip',
   'DATA'),
  ('QtCore', 'PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore', 'SYMLINK'),
  ('QtGui', 'PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui', 'SYMLINK'),
  ('QtDBus', 'PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus', 'SYMLINK'),
  ('QtQmlModels',
   'PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'SYMLINK'),
  ('QtQml', 'PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml', 'SYMLINK'),
  ('QtWebSockets',
   'PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'SYMLINK'),
  ('QtNetwork',
   'PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'SYMLINK'),
  ('QtQuick', 'PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick', 'SYMLINK'),
  ('QtWidgets',
   'PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'SYMLINK'),
  ('QtSvg', 'PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg', 'SYMLINK'),
  ('QtPrintSupport',
   'PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/QtPrintSupport',
   'Versions/Current/QtPrintSupport',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/QtQml', 'Versions/Current/QtQml', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQml.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/QtQmlModels',
   'Versions/Current/QtQmlModels',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/QtQuick',
   'Versions/Current/QtQuick',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/QtWebSockets',
   'Versions/Current/QtWebSockets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/Current', '5', 'SYMLINK'),
  ('Python3.framework/Python3', 'Versions/Current/Python3', 'SYMLINK'),
  ('Python3.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python3.framework/Versions/3.9/Resources/Info.plist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Resources/Info.plist',
   'DATA'),
  ('Python3.framework/Versions/Current', '3.9', 'SYMLINK')],
 [('heapq',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/heapq.py',
   'PYMODULE'),
  ('copyreg',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/copyreg.py',
   'PYMODULE'),
  ('weakref',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/weakref.py',
   'PYMODULE'),
  ('genericpath',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/genericpath.py',
   'PYMODULE'),
  ('sre_parse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sre_parse.py',
   'PYMODULE'),
  ('sre_compile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sre_compile.py',
   'PYMODULE'),
  ('locale',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/locale.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/idna.py',
   'PYMODULE'),
  ('encodings.hz',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/aliases.py',
   'PYMODULE'),
  ('encodings',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/encodings/__init__.py',
   'PYMODULE'),
  ('stat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/stat.py',
   'PYMODULE'),
  ('warnings',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/warnings.py',
   'PYMODULE'),
  ('abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/abc.py',
   'PYMODULE'),
  ('sre_constants',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sre_constants.py',
   'PYMODULE'),
  ('types',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/types.py',
   'PYMODULE'),
  ('keyword',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/keyword.py',
   'PYMODULE'),
  ('enum',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/enum.py',
   'PYMODULE'),
  ('io',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/io.py',
   'PYMODULE'),
  ('_weakrefset',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_weakrefset.py',
   'PYMODULE'),
  ('_collections_abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_collections_abc.py',
   'PYMODULE'),
  ('_bootlocale',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_bootlocale.py',
   'PYMODULE'),
  ('posixpath',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/posixpath.py',
   'PYMODULE'),
  ('operator',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/operator.py',
   'PYMODULE'),
  ('reprlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/reprlib.py',
   'PYMODULE'),
  ('codecs',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/codecs.py',
   'PYMODULE'),
  ('ntpath',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ntpath.py',
   'PYMODULE'),
  ('functools',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/functools.py',
   'PYMODULE'),
  ('collections.abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/collections/abc.py',
   'PYMODULE'),
  ('collections',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/collections/__init__.py',
   'PYMODULE'),
  ('linecache',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/linecache.py',
   'PYMODULE'),
  ('re',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/re.py',
   'PYMODULE'),
  ('traceback',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/traceback.py',
   'PYMODULE'),
  ('os',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/os.py',
   'PYMODULE')])
