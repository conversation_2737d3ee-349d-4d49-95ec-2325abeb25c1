('/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/Excel数据处理工具.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('excel_pyqt5_app',
   '/Users/<USER>/Documents/work/cursor-workspace/excel_pyqt5_app.py',
   'PYSOURCE'),
  ('Python3.framework/Versions/3.9/Python3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Python3',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('lib-dynload/_posixshmem.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixshmem.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/pyexpat.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_scproxy.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/termios.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/binascii.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_statistics.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha512.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_random.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/math.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ssl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_hashlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha3.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_blake2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha256.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_md5.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha1.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bisect.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/unicodedata.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_contextvars.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_decimal.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/array.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/select.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_csv.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/resource.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/grp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_lzma.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bz2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/mmap.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ctypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_queue.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_pickle.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_opcode.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_heapq.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtGui.abi3.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt5/sip.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/sip.cpython-39-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtCore.abi3.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/QtCore.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtWidgets.abi3.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/QtWidgets.abi3.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/cmath.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/writers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/writers.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_asyncio.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/readline.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_multiarray_umath.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/_umath_linalg.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_elementtree.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_uuid.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sqlite3.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_multiarray_tests.cpython-39-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/md__mypyc.cpython-39-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/md.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/mtrand.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_sfc64.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_philox.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_pcg64.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_mt19937.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/bit_generator.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_generator.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_bounded_integers.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_common.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_pocketfft_umath.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/indexers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/indexers.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/aggregations.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/aggregations.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/vectorized.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/vectorized.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/tzconversion.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/tzconversion.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timezones.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timezones.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timestamps.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timestamps.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timedeltas.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timedeltas.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/strptime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/strptime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/period.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/period.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/parsing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/parsing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/offsets.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/offsets.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/np_datetime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/np_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/nattype.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/nattype.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/fields.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/fields.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/dtypes.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/dtypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/conversion.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/conversion.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/ccalendar.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/ccalendar.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/base.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/base.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslib.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslib.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/testing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/testing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sparse.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/sparse.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sas.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/sas.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/reshape.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/reshape.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/properties.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/properties.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/parsers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/parsers.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_parser.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/pandas_parser.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_datetime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/pandas_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops_dispatch.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/ops_dispatch.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/ops.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/missing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/missing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/lib.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/lib.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/json.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/json.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/join.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/join.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/interval.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/interval.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/internals.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/internals.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/indexing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/indexing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/index.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/index.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashtable.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/hashtable.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/hashing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/groupby.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/groupby.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/byteswap.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/byteswap.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/arrays.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/arrays.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/algos.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/algos.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_json.cpython-39-darwin.so',
   'EXTENSION'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'BINARY'),
  ('PyQt5/Qt5/translations/qt_help_nl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ar.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_TW.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_uk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_CN.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_de.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ar.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_PT.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_da.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_en.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_he.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_it.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gd.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ja.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_uk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_de.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_es.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_cs.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fa.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nn.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ru.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nn.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ko.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_es.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ru.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_gd.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fi.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_da.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_lv.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ja.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lv.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_de.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ca.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ja.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_bg.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fa.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hu.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_he.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fi.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ko.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ar.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hu.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_it.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_bg.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hu.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lt.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_lt.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_en.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_gl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_fr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_BR.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ko.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nl.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_es.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_tr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sv.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_sv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ca.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nn.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_bg.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_cs.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ca.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_en.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_it.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_tr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_tr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_cs.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_da.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_uk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_sk.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hr.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ru.qm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/translations/qt_help_ru.qm',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas/io/formats/templates/latex.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_table.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_longtable.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex_longtable.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_table.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/string.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/string.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_style.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html_style.tpl',
   'DATA'),
  ('pytz/zoneinfo/Europe/Moscow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Moscow',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Palmer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Palmer',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Easter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Easter',
   'DATA'),
  ('pytz/zoneinfo/Africa/Malabo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Malabo',
   'DATA'),
  ('pytz/zoneinfo/America/Godthab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Godthab',
   'DATA'),
  ('pytz/zoneinfo/Europe/Podgorica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Podgorica',
   'DATA'),
  ('pytz/zoneinfo/Asia/Harbin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Harbin',
   'DATA'),
  ('pytz/zoneinfo/America/Jujuy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kampala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kampala',
   'DATA'),
  ('pytz/zoneinfo/Africa/Cairo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Cairo',
   'DATA'),
  ('pytz/zoneinfo/GB',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GB',
   'DATA'),
  ('pytz/zoneinfo/Europe/Warsaw',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Warsaw',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Niue',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Niue',
   'DATA'),
  ('pytz/zoneinfo/Etc/Zulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Mexico/General',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/General',
   'DATA'),
  ('pytz/zoneinfo/America/Danmarkshavn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Danmarkshavn',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sarajevo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Sarajevo',
   'DATA'),
  ('pytz/zoneinfo/PRC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/PRC',
   'DATA'),
  ('pytz/zoneinfo/Etc/UCT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/UCT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Barnaul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Barnaul',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lord_Howe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Lord_Howe',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Mawson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Mawson',
   'DATA'),
  ('pytz/zoneinfo/America/Panama',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Panama',
   'DATA'),
  ('pytz/zoneinfo/America/Cuiaba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cuiaba',
   'DATA'),
  ('pytz/zoneinfo/America/El_Salvador',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/El_Salvador',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kirov',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kirov',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pitcairn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pitcairn',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Indianapolis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Australia/Queensland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Queensland',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bangkok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bangkok',
   'DATA'),
  ('pytz/zoneinfo/America/Louisville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Louisville',
   'DATA'),
  ('pytz/zoneinfo/EET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EET',
   'DATA'),
  ('pytz/zoneinfo/Turkey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Turkey',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vevay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Vevay',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chuuk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Chuuk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pago_Pago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pago_Pago',
   'DATA'),
  ('pytz/zoneinfo/America/Santiago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santiago',
   'DATA'),
  ('pytz/zoneinfo/America/Detroit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Detroit',
   'DATA'),
  ('pytz/zoneinfo/America/Creston',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Creston',
   'DATA'),
  ('pytz/zoneinfo/Australia/Yancowinna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Yancowinna',
   'DATA'),
  ('pytz/zoneinfo/Canada/Atlantic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Atlantic',
   'DATA'),
  ('pytz/zoneinfo/America/Edmonton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Edmonton',
   'DATA'),
  ('pytz/zoneinfo/Europe/Lisbon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Lisbon',
   'DATA'),
  ('pytz/zoneinfo/America/Boise',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Boise',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baku',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Baku',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Luis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/San_Luis',
   'DATA'),
  ('pytz/zoneinfo/EST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EST',
   'DATA'),
  ('pytz/zoneinfo/Arctic/Longyearbyen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Arctic/Longyearbyen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bahrain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bahrain',
   'DATA'),
  ('pytz/zoneinfo/America/Mazatlan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mazatlan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Urumqi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Urumqi',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kiritimati',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kiritimati',
   'DATA'),
  ('pytz/zoneinfo/GMT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT',
   'DATA'),
  ('pytz/zoneinfo/Europe/Malta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Malta',
   'DATA'),
  ('pytz/zoneinfo/America/Atka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Atka',
   'DATA'),
  ('pytz/zoneinfo/America/Guatemala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guatemala',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-6',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-6',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Yap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Yap',
   'DATA'),
  ('pytz/zoneinfo/America/Vancouver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Vancouver',
   'DATA'),
  ('pytz/zoneinfo/America/Lima',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Lima',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Winamac',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Winamac',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faroe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Faroe',
   'DATA'),
  ('pytz/zoneinfo/US/East-Indiana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/East-Indiana',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Mendoza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Asia/Istanbul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Calcutta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Calcutta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Budapest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Budapest',
   'DATA'),
  ('pytz/zoneinfo/Africa/Luanda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Luanda',
   'DATA'),
  ('pytz/zoneinfo/America/Santarem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santarem',
   'DATA'),
  ('pytz/zoneinfo/Indian/Kerguelen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Kerguelen',
   'DATA'),
  ('pytz/zoneinfo/America/Campo_Grande',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Campo_Grande',
   'DATA'),
  ('pytz/zoneinfo/MET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MET',
   'DATA'),
  ('pytz/zoneinfo/America/Nuuk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nuuk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tarawa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tarawa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bucharest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Bucharest',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/South_Georgia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/South_Georgia',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-7',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-7',
   'DATA'),
  ('pytz/zoneinfo/Europe/Riga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Riga',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulan_Bator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ulan_Bator',
   'DATA'),
  ('pytz/zoneinfo/America/Goose_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Goose_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zaporozhye',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zaporozhye',
   'DATA'),
  ('pytz/zoneinfo/Asia/Almaty',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Almaty',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yakutsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yakutsk',
   'DATA'),
  ('pytz/zoneinfo/America/Jamaica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Indian/Christmas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Christmas',
   'DATA'),
  ('pytz/zoneinfo/America/Nassau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nassau',
   'DATA'),
  ('pytz/zoneinfo/America/Managua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Managua',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Nauru',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Nauru',
   'DATA'),
  ('pytz/zoneinfo/America/Merida',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Merida',
   'DATA'),
  ('pytz/zoneinfo/Europe/Istanbul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Africa/Abidjan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Abidjan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chongqing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chongqing',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+1',
   'DATA'),
  ('pytz/zoneinfo/Asia/Singapore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vatican',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vatican',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dili',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dili',
   'DATA'),
  ('pytz/zoneinfo/Asia/Oral',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Oral',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nairobi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Nairobi',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kiev',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kiev',
   'DATA'),
  ('pytz/zoneinfo/Europe/Rome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Rome',
   'DATA'),
  ('pytz/zoneinfo/America/Rio_Branco',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rio_Branco',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+3',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT',
   'DATA'),
  ('pytz/zoneinfo/Singapore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Truk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Truk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Mariehamn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Mariehamn',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tbilisi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tbilisi',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-11',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Buenos_Aires',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tahiti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tahiti',
   'DATA'),
  ('pytz/zoneinfo/Brazil/West',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/West',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ljubljana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Ljubljana',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qyzylorda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qyzylorda',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pontianak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Pontianak',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dar_es_Salaam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Dar_es_Salaam',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ndjamena',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ndjamena',
   'DATA'),
  ('pytz/zoneinfo/America/Halifax',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Halifax',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Canary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Canary',
   'DATA'),
  ('pytz/zoneinfo/America/Phoenix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Phoenix',
   'DATA'),
  ('pytz/zoneinfo/Europe/Helsinki',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Helsinki',
   'DATA'),
  ('pytz/zoneinfo/Asia/Sakhalin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Sakhalin',
   'DATA'),
  ('pytz/zoneinfo/America/Miquelon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Miquelon',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fakaofo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Fakaofo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Omsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Omsk',
   'DATA'),
  ('pytz/zoneinfo/America/Guayaquil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guayaquil',
   'DATA'),
  ('pytz/zoneinfo/Australia/Darwin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Darwin',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aqtau',
   'DATA'),
  ('pytz/zoneinfo/America/Atikokan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Atikokan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Dublin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Dublin',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaSur',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/BajaSur',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-4',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-4',
   'DATA'),
  ('pytz/zoneinfo/Indian/Cocos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Cocos',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Marquesas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Marquesas',
   'DATA'),
  ('pytz/zoneinfo/Australia/Sydney',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Sydney',
   'DATA'),
  ('pytz/zoneinfo/America/Cayenne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cayenne',
   'DATA'),
  ('pytz/zoneinfo/Australia/Canberra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Canberra',
   'DATA'),
  ('pytz/zoneinfo/Africa/Niamey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Niamey',
   'DATA'),
  ('pytz/zoneinfo/America/Caracas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Caracas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yerevan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yerevan',
   'DATA'),
  ('pytz/zoneinfo/America/Araguaina',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Araguaina',
   'DATA'),
  ('pytz/zoneinfo/America/Nipigon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nipigon',
   'DATA'),
  ('pytz/zoneinfo/America/St_Lucia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Lucia',
   'DATA'),
  ('pytz/zoneinfo/Australia/Hobart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Hobart',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-13',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-13',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mahe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mahe',
   'DATA'),
  ('pytz/zoneinfo/Africa/Johannesburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Johannesburg',
   'DATA'),
  ('pytz/zoneinfo/Australia/Broken_Hill',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Broken_Hill',
   'DATA'),
  ('pytz/zoneinfo/Indian/Reunion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Reunion',
   'DATA'),
  ('pytz/zoneinfo/America/Lower_Princes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Lower_Princes',
   'DATA'),
  ('pytz/zoneinfo/Greenwich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kwajalein',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Asia/Katmandu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Katmandu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Minsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Minsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Monrovia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Monrovia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lubumbashi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lubumbashi',
   'DATA'),
  ('pytz/zoneinfo/Europe/Volgograd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Volgograd',
   'DATA'),
  ('pytz/zoneinfo/tzdata.zi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/tzdata.zi',
   'DATA'),
  ('pytz/zoneinfo/Australia/Victoria',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Victoria',
   'DATA'),
  ('pytz/zoneinfo/America/Aruba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Aruba',
   'DATA'),
  ('pytz/zoneinfo/America/St_Johns',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Johns',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-14',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-14',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-2',
   'DATA'),
  ('pytz/zoneinfo/Europe/Athens',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Athens',
   'DATA'),
  ('pytz/zoneinfo/America/Guyana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guyana',
   'DATA'),
  ('pytz/zoneinfo/America/Metlakatla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Metlakatla',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qostanay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qostanay',
   'DATA'),
  ('pytz/zoneinfo/America/Rankin_Inlet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rankin_Inlet',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yekaterinburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yekaterinburg',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+8',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+8',
   'DATA'),
  ('pytz/zoneinfo/Canada/Mountain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Mountain',
   'DATA'),
  ('pytz/zoneinfo/zone.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zone.tab',
   'DATA'),
  ('pytz/zoneinfo/America/Cayman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cayman',
   'DATA'),
  ('pytz/zoneinfo/Europe/Chisinau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Chisinau',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuching',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuching',
   'DATA'),
  ('pytz/zoneinfo/Iran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Iran',
   'DATA'),
  ('pytz/zoneinfo/CST6CDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/CST6CDT',
   'DATA'),
  ('pytz/zoneinfo/America/Grand_Turk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Grand_Turk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Shanghai',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Shanghai',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimbu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Thimbu',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+10',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+10',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tripoli',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Tripoli',
   'DATA'),
  ('pytz/zoneinfo/America/Nome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nome',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Velho',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Porto_Velho',
   'DATA'),
  ('pytz/zoneinfo/America/Hermosillo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Hermosillo',
   'DATA'),
  ('pytz/zoneinfo/America/Eirunepe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Eirunepe',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/New_Salem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/New_Salem',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pyongyang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Pyongyang',
   'DATA'),
  ('pytz/zoneinfo/Europe/Monaco',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Monaco',
   'DATA'),
  ('pytz/zoneinfo/US/Mountain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/St_Helena',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/St_Helena',
   'DATA'),
  ('pytz/zoneinfo/Chile/EasterIsland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Chile/EasterIsland',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Tell_City',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Tell_City',
   'DATA'),
  ('pytz/zoneinfo/Asia/Gaza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Gaza',
   'DATA'),
  ('pytz/zoneinfo/Portugal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Portugal',
   'DATA'),
  ('pytz/zoneinfo/America/Sitka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Sitka',
   'DATA'),
  ('pytz/zoneinfo/Africa/Porto-Novo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Porto-Novo',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Petersburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Petersburg',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/McMurdo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/McMurdo',
   'DATA'),
  ('pytz/zoneinfo/America/Belem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Belem',
   'DATA'),
  ('pytz/zoneinfo/Africa/Juba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Juba',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Madeira',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Madeira',
   'DATA'),
  ('pytz/zoneinfo/America/Catamarca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dhaka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dhaka',
   'DATA'),
  ('pytz/zoneinfo/America/Monterrey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Monterrey',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimphu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Thimphu',
   'DATA'),
  ('pytz/zoneinfo/iso3166.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/iso3166.tab',
   'DATA'),
  ('pytz/zoneinfo/Europe/Amsterdam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Amsterdam',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+2',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Ushuaia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Ushuaia',
   'DATA'),
  ('pytz/zoneinfo/America/Moncton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Moncton',
   'DATA'),
  ('pytz/zoneinfo/Australia/Melbourne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Melbourne',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jerusalem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jerusalem',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pohnpei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pohnpei',
   'DATA'),
  ('pytz/zoneinfo/America/Kralendijk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kralendijk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Ponape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Ponape',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kanton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kanton',
   'DATA'),
  ('pytz/zoneinfo/Etc/Universal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Universal',
   'DATA'),
  ('pytz/zoneinfo/leapseconds',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/leapseconds',
   'DATA'),
  ('pytz/zoneinfo/Asia/Nicosia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Indian/Antananarivo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Antananarivo',
   'DATA'),
  ('pytz/zoneinfo/GMT+0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/America/Asuncion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Asuncion',
   'DATA'),
  ('pytz/zoneinfo/America/Shiprock',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Shiprock',
   'DATA'),
  ('pytz/zoneinfo/America/Whitehorse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Whitehorse',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Rothera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Rothera',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Palau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Palau',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Syowa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Syowa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kaliningrad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kaliningrad',
   'DATA'),
  ('pytz/zoneinfo/America/Port_of_Spain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Port_of_Spain',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+5',
   'DATA'),
  ('pytz/zoneinfo/America/Denver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Denver',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ulyanovsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Ulyanovsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Addis_Ababa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Addis_Ababa',
   'DATA'),
  ('pytz/zoneinfo/America/Ensenada',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ensenada',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maputo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Maputo',
   'DATA'),
  ('pytz/zoneinfo/America/Grenada',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Grenada',
   'DATA'),
  ('pytz/zoneinfo/America/Anguilla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Anguilla',
   'DATA'),
  ('pytz/zoneinfo/America/Tijuana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tijuana',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kinshasa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kinshasa',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tongatapu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tongatapu',
   'DATA'),
  ('pytz/zoneinfo/America/Bogota',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bogota',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Samoa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Troll',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Troll',
   'DATA'),
  ('pytz/zoneinfo/Asia/Muscat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Muscat',
   'DATA'),
  ('pytz/zoneinfo/Cuba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Cuba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Famagusta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Famagusta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vienna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vienna',
   'DATA'),
  ('pytz/zoneinfo/Europe/Simferopol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Simferopol',
   'DATA'),
  ('pytz/zoneinfo/America/Virgin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Virgin',
   'DATA'),
  ('pytz/zoneinfo/America/Pangnirtung',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Pangnirtung',
   'DATA'),
  ('pytz/zoneinfo/Israel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Israel',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashgabat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ashgabat',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zagreb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zagreb',
   'DATA'),
  ('pytz/zoneinfo/Europe/Astrakhan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Astrakhan',
   'DATA'),
  ('pytz/zoneinfo/America/Indianapolis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kyiv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kyiv',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wallis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Wallis',
   'DATA'),
  ('pytz/zoneinfo/Universal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/Montevideo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montevideo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+6',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+6',
   'DATA'),
  ('pytz/zoneinfo/Africa/Khartoum',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Khartoum',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zurich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zurich',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kabul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kabul',
   'DATA'),
  ('pytz/zoneinfo/America/Rosario',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rosario',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tokyo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tokyo',
   'DATA'),
  ('pytz/zoneinfo/America/Boa_Vista',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Boa_Vista',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tomsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tomsk',
   'DATA'),
  ('pytz/zoneinfo/Zulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Zulu',
   'DATA'),
  ('pytz/zoneinfo/America/Tortola',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tortola',
   'DATA'),
  ('pytz/zoneinfo/Africa/Conakry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Conakry',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dubai',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dubai',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bamako',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bamako',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulaanbaatar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ulaanbaatar',
   'DATA'),
  ('pytz/zoneinfo/Factory',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Factory',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chita',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chita',
   'DATA'),
  ('pytz/zoneinfo/Asia/Makassar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Makassar',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-9',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-9',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baghdad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Baghdad',
   'DATA'),
  ('pytz/zoneinfo/America/Tegucigalpa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tegucigalpa',
   'DATA'),
  ('pytz/zoneinfo/Poland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Poland',
   'DATA'),
  ('pytz/zoneinfo/ROK',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/ROK',
   'DATA'),
  ('pytz/zoneinfo/Europe/Oslo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Oslo',
   'DATA'),
  ('pytz/zoneinfo/US/Eastern',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Eastern',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Louisville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kentucky/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/South_Pole',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/South_Pole',
   'DATA'),
  ('pytz/zoneinfo/US/Alaska',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Alaska',
   'DATA'),
  ('pytz/zoneinfo/Europe/Jersey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Jersey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Berlin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Berlin',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lusaka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lusaka',
   'DATA'),
  ('pytz/zoneinfo/Africa/Freetown',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Freetown',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bujumbura',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bujumbura',
   'DATA'),
  ('pytz/zoneinfo/Etc/Greenwich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vilnius',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vilnius',
   'DATA'),
  ('pytz/zoneinfo/Jamaica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ujung_Pandang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ujung_Pandang',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuala_Lumpur',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuala_Lumpur',
   'DATA'),
  ('pytz/zoneinfo/Europe/Busingen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Busingen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuwait',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuwait',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Rarotonga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Rarotonga',
   'DATA'),
  ('pytz/zoneinfo/America/Adak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Adak',
   'DATA'),
  ('pytz/zoneinfo/Europe/Brussels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Brussels',
   'DATA'),
  ('pytz/zoneinfo/America/Puerto_Rico',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Puerto_Rico',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Apia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Apia',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kosrae',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kosrae',
   'DATA'),
  ('pytz/zoneinfo/Brazil/Acre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/Acre',
   'DATA'),
  ('pytz/zoneinfo/America/Juneau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Juneau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Banjul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Banjul',
   'DATA'),
  ('pytz/zoneinfo/America/Antigua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Antigua',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vincennes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Vincennes',
   'DATA'),
  ('pytz/zoneinfo/WET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/WET',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tunis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Tunis',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ouagadougou',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ouagadougou',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tiraspol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tiraspol',
   'DATA'),
  ('pytz/zoneinfo/Asia/Krasnoyarsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Krasnoyarsk',
   'DATA'),
  ('pytz/zoneinfo/Australia/Tasmania',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Tasmania',
   'DATA'),
  ('pytz/zoneinfo/Australia/Brisbane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Brisbane',
   'DATA'),
  ('pytz/zoneinfo/Europe/Gibraltar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Gibraltar',
   'DATA'),
  ('pytz/zoneinfo/CET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/CET',
   'DATA'),
  ('pytz/zoneinfo/US/Samoa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novosibirsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Novosibirsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tallinn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tallinn',
   'DATA'),
  ('pytz/zoneinfo/Australia/Currie',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Currie',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tashkent',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tashkent',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Wayne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fort_Wayne',
   'DATA'),
  ('pytz/zoneinfo/W-SU',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/W-SU',
   'DATA'),
  ('pytz/zoneinfo/America/Iqaluit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Iqaluit',
   'DATA'),
  ('pytz/zoneinfo/ROC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/ROC',
   'DATA'),
  ('pytz/zoneinfo/Asia/Seoul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Seoul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Phnom_Penh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Phnom_Penh',
   'DATA'),
  ('pytz/zoneinfo/America/New_York',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/New_York',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Jan_Mayen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Jan_Mayen',
   'DATA'),
  ('pytz/zoneinfo/Africa/Brazzaville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Brazzaville',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Nelson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fort_Nelson',
   'DATA'),
  ('pytz/zoneinfo/Canada/Saskatchewan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Saskatchewan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chatham',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Chatham',
   'DATA'),
  ('pytz/zoneinfo/Europe/San_Marino',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/San_Marino',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bangui',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bangui',
   'DATA'),
  ('pytz/zoneinfo/Libya',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Libya',
   'DATA'),
  ('pytz/zoneinfo/America/Knox_IN',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Knox_IN',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mauritius',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mauritius',
   'DATA'),
  ('pytz/zoneinfo/Africa/Blantyre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Blantyre',
   'DATA'),
  ('pytz/zoneinfo/Africa/Libreville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Libreville',
   'DATA'),
  ('pytz/zoneinfo/America/Havana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Havana',
   'DATA'),
  ('pytz/zoneinfo/Iceland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Iceland',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dawson',
   'DATA'),
  ('pytz/zoneinfo/PST8PDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/PST8PDT',
   'DATA'),
  ('pytz/zoneinfo/America/Barbados',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Barbados',
   'DATA'),
  ('pytz/zoneinfo/Asia/Taipei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Taipei',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hebron',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hebron',
   'DATA'),
  ('pytz/zoneinfo/Asia/Samarkand',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Samarkand',
   'DATA'),
  ('pytz/zoneinfo/America/Menominee',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Menominee',
   'DATA'),
  ('pytz/zoneinfo/America/Montreal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montreal',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Enderbury',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Enderbury',
   'DATA'),
  ('pytz/zoneinfo/America/Santa_Isabel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santa_Isabel',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtobe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aqtobe',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dakar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Dakar',
   'DATA'),
  ('pytz/zoneinfo/Australia/Eucla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Eucla',
   'DATA'),
  ('pytz/zoneinfo/America/Cambridge_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cambridge_Bay',
   'DATA'),
  ('pytz/zoneinfo/Africa/Harare',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Harare',
   'DATA'),
  ('pytz/zoneinfo/America/Chihuahua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Chihuahua',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bissau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bissau',
   'DATA'),
  ('pytz/zoneinfo/America/Costa_Rica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Costa_Rica',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/ComodRivadavia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/ComodRivadavia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macao',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Macao',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-5',
   'DATA'),
  ('pytz/zoneinfo/Europe/Paris',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Paris',
   'DATA'),
  ('pytz/zoneinfo/America/Punta_Arenas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Punta_Arenas',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Efate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Efate',
   'DATA'),
  ('pytz/zoneinfo/America/Mexico_City',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mexico_City',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novokuznetsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Novokuznetsk',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Macquarie',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Macquarie',
   'DATA'),
  ('pytz/zoneinfo/America/Santo_Domingo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santo_Domingo',
   'DATA'),
  ('pytz/zoneinfo/Europe/London',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/London',
   'DATA'),
  ('pytz/zoneinfo/Canada/Yukon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Yukon',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia_Banderas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bahia_Banderas',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fiji',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Fiji',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Galapagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Galapagos',
   'DATA'),
  ('pytz/zoneinfo/Indian/Maldives',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Maldives',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-8',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-8',
   'DATA'),
  ('pytz/zoneinfo/HST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/HST',
   'DATA'),
  ('pytz/zoneinfo/Asia/Saigon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Saigon',
   'DATA'),
  ('pytz/zoneinfo/Africa/El_Aaiun',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/El_Aaiun',
   'DATA'),
  ('pytz/zoneinfo/America/Buenos_Aires',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bahia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mogadishu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Mogadishu',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Cordoba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/America/Ojinaga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ojinaga',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ust-Nera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ust-Nera',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Funafuti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Funafuti',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bishkek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bishkek',
   'DATA'),
  ('pytz/zoneinfo/America/Matamoros',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Matamoros',
   'DATA'),
  ('pytz/zoneinfo/America/Resolute',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Resolute',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-1',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Knox',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Knox',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vientiane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Vientiane',
   'DATA'),
  ('pytz/zoneinfo/America/Glace_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Glace_Bay',
   'DATA'),
  ('pytz/zoneinfo/Asia/Beirut',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Beirut',
   'DATA'),
  ('pytz/zoneinfo/America/Sao_Paulo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Sao_Paulo',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Johnston',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Johnston',
   'DATA'),
  ('pytz/zoneinfo/Australia/Adelaide',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Adelaide',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Bougainville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Bougainville',
   'DATA'),
  ('pytz/zoneinfo/GMT-0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/America/Yakutat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Yakutat',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lindeman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Lindeman',
   'DATA'),
  ('pytz/zoneinfo/MST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MST',
   'DATA'),
  ('pytz/zoneinfo/America/St_Barthelemy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Barthelemy',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Azores',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Azores',
   'DATA'),
  ('pytz/zoneinfo/America/Los_Angeles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Los_Angeles',
   'DATA'),
  ('pytz/zoneinfo/Australia/NSW',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/NSW',
   'DATA'),
  ('pytz/zoneinfo/America/Ciudad_Juarez',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ciudad_Juarez',
   'DATA'),
  ('pytz/zoneinfo/Africa/Casablanca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Casablanca',
   'DATA'),
  ('pytz/zoneinfo/Europe/Madrid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Madrid',
   'DATA'),
  ('pytz/zoneinfo/MST7MDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MST7MDT',
   'DATA'),
  ('pytz/zoneinfo/America/Fortaleza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fortaleza',
   'DATA'),
  ('pytz/zoneinfo/Asia/Brunei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Brunei',
   'DATA'),
  ('pytz/zoneinfo/Europe/Prague',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Prague',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Acre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Porto_Acre',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Reykjavik',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Reykjavik',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson_Creek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dawson_Creek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kathmandu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kathmandu',
   'DATA'),
  ('pytz/zoneinfo/America/Curacao',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Curacao',
   'DATA'),
  ('pytz/zoneinfo/America/Cancun',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cancun',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hovd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hovd',
   'DATA'),
  ('pytz/zoneinfo/Africa/Windhoek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Windhoek',
   'DATA'),
  ('pytz/zoneinfo/Europe/Copenhagen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Copenhagen',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mayotte',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mayotte',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ceuta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ceuta',
   'DATA'),
  ('pytz/zoneinfo/America/Chicago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Chicago',
   'DATA'),
  ('pytz/zoneinfo/Asia/Anadyr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Anadyr',
   'DATA'),
  ('pytz/zoneinfo/Australia/LHI',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/LHI',
   'DATA'),
  ('pytz/zoneinfo/Brazil/DeNoronha',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/DeNoronha',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kolkata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kolkata',
   'DATA'),
  ('pytz/zoneinfo/Asia/Amman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Amman',
   'DATA'),
  ('pytz/zoneinfo/America/St_Vincent',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Vincent',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Majuro',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Majuro',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Auckland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Auckland',
   'DATA'),
  ('pytz/zoneinfo/Canada/Pacific',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Dominica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dominica',
   'DATA'),
  ('pytz/zoneinfo/US/Hawaii',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Hawaii',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Honolulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Honolulu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Guernsey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Guernsey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Stockholm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Stockholm',
   'DATA'),
  ('pytz/zoneinfo/US/Central',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Central',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+11',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tirane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tirane',
   'DATA'),
  ('pytz/zoneinfo/America/Coyhaique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Coyhaique',
   'DATA'),
  ('pytz/zoneinfo/Etc/UTC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/UTC',
   'DATA'),
  ('pytz/zoneinfo/UCT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/UCT',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+9',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+9',
   'DATA'),
  ('pytz/zoneinfo/zonenow.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zonenow.tab',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Center',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/Center',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faeroe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Faeroe',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vaduz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vaduz',
   'DATA'),
  ('pytz/zoneinfo/Asia/Irkutsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Irkutsk',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Stanley',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Stanley',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belgrade',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Belgrade',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kamchatka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kamchatka',
   'DATA'),
  ('pytz/zoneinfo/America/Toronto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Toronto',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Jujuy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nouakchott',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Nouakchott',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dushanbe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dushanbe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Manila',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Manila',
   'DATA'),
  ('pytz/zoneinfo/America/Regina',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Regina',
   'DATA'),
  ('pytz/zoneinfo/EST5EDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EST5EDT',
   'DATA'),
  ('pytz/zoneinfo/America/St_Kitts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Kitts',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmara',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Asmara',
   'DATA'),
  ('pytz/zoneinfo/Europe/Saratov',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Saratov',
   'DATA'),
  ('pytz/zoneinfo/UTC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/UTC',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Midway',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Midway',
   'DATA'),
  ('pytz/zoneinfo/Europe/Luxembourg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Luxembourg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chungking',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chungking',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Bermuda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Bermuda',
   'DATA'),
  ('pytz/zoneinfo/America/Scoresbysund',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Scoresbysund',
   'DATA'),
  ('pytz/zoneinfo/America/Noronha',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Noronha',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-3',
   'DATA'),
  ('pytz/zoneinfo/GB-Eire',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GB-Eire',
   'DATA'),
  ('pytz/zoneinfo/Asia/Damascus',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Damascus',
   'DATA'),
  ('pytz/zoneinfo/GMT0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qatar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qatar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ho_Chi_Minh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ho_Chi_Minh',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Saipan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Saipan',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Tucuman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Tucuman',
   'DATA'),
  ('pytz/zoneinfo/America/Blanc-Sablon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Blanc-Sablon',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Noumea',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Noumea',
   'DATA'),
  ('pytz/zoneinfo/Australia/South',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/South',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Gambier',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Gambier',
   'DATA'),
  ('pytz/zoneinfo/US/Aleutian',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Aleutian',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lome',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-12',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-12',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lagos',
   'DATA'),
  ('pytz/zoneinfo/America/Marigot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Marigot',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maseru',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Maseru',
   'DATA'),
  ('pytz/zoneinfo/Chile/Continental',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Chile/Continental',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Australia/West',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/West',
   'DATA'),
  ('pytz/zoneinfo/Europe/Isle_of_Man',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Isle_of_Man',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Norfolk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Norfolk',
   'DATA'),
  ('pytz/zoneinfo/US/Indiana-Starke',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Indiana-Starke',
   'DATA'),
  ('pytz/zoneinfo/Europe/Andorra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Andorra',
   'DATA'),
  ('pytz/zoneinfo/America/Thule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Thule',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hong_Kong',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hong_Kong',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tehran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tehran',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vladivostok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Vladivostok',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/DumontDUrville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/DumontDUrville',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Marengo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Marengo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mbabane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Mbabane',
   'DATA'),
  ('pytz/zoneinfo/Europe/Uzhgorod',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Uzhgorod',
   'DATA'),
  ('pytz/zoneinfo/Asia/Karachi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Karachi',
   'DATA'),
  ('pytz/zoneinfo/Australia/North',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/North',
   'DATA'),
  ('pytz/zoneinfo/America/Manaus',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Manaus',
   'DATA'),
  ('pytz/zoneinfo/Africa/Accra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Accra',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jakarta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jakarta',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Salta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Salta',
   'DATA'),
  ('pytz/zoneinfo/Africa/Gaborone',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Gaborone',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wake',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Wake',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Catamarca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Asia/Colombo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Colombo',
   'DATA'),
  ('pytz/zoneinfo/America/Cordoba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/America/Martinique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Martinique',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-10',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-10',
   'DATA'),
  ('pytz/zoneinfo/America/Anchorage',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Anchorage',
   'DATA'),
  ('pytz/zoneinfo/Europe/Nicosia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Cape_Verde',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Cape_Verde',
   'DATA'),
  ('pytz/zoneinfo/Australia/Perth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Perth',
   'DATA'),
  ('pytz/zoneinfo/zone1970.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zone1970.tab',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guadalcanal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Guadalcanal',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaNorte',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/BajaNorte',
   'DATA'),
  ('pytz/zoneinfo/America/Guadeloupe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guadeloupe',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Juan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/San_Juan',
   'DATA'),
  ('pytz/zoneinfo/Indian/Comoro',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Comoro',
   'DATA'),
  ('pytz/zoneinfo/America/Coral_Harbour',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Coral_Harbour',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Monticello',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kentucky/Monticello',
   'DATA'),
  ('pytz/zoneinfo/America/Inuvik',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Inuvik',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Vostok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Vostok',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yangon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yangon',
   'DATA'),
  ('pytz/zoneinfo/Africa/Timbuktu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Timbuktu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kashgar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kashgar',
   'DATA'),
  ('pytz/zoneinfo/America/La_Paz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/La_Paz',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jayapura',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jayapura',
   'DATA'),
  ('pytz/zoneinfo/Australia/ACT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/ACT',
   'DATA'),
  ('pytz/zoneinfo/US/Arizona',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Arizona',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aden',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aden',
   'DATA'),
  ('pytz/zoneinfo/America/Paramaribo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Paramaribo',
   'DATA'),
  ('pytz/zoneinfo/America/Maceio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Maceio',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Beulah',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/Beulah',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Davis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Davis',
   'DATA'),
  ('pytz/zoneinfo/Asia/Rangoon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Rangoon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Skopje',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Skopje',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bratislava',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Bratislava',
   'DATA'),
  ('pytz/zoneinfo/America/Swift_Current',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Swift_Current',
   'DATA'),
  ('pytz/zoneinfo/America/Mendoza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Casey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Casey',
   'DATA'),
  ('pytz/zoneinfo/Kwajalein',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Asmera',
   'DATA'),
  ('pytz/zoneinfo/America/St_Thomas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Thomas',
   'DATA'),
  ('pytz/zoneinfo/US/Pacific',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Pacific',
   'DATA'),
  ('pytz/zoneinfo/Canada/Central',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Central',
   'DATA'),
  ('pytz/zoneinfo/Eire',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Eire',
   'DATA'),
  ('pytz/zoneinfo/NZ-CHAT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/NZ-CHAT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Riyadh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Riyadh',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/La_Rioja',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/La_Rioja',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Guam',
   'DATA'),
  ('pytz/zoneinfo/America/Port-au-Prince',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Port-au-Prince',
   'DATA'),
  ('pytz/zoneinfo/Asia/Choibalsan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Choibalsan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dacca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dacca',
   'DATA'),
  ('pytz/zoneinfo/America/Rainy_River',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rainy_River',
   'DATA'),
  ('pytz/zoneinfo/America/Belize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Belize',
   'DATA'),
  ('pytz/zoneinfo/America/Yellowknife',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Yellowknife',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kigali',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kigali',
   'DATA'),
  ('pytz/zoneinfo/US/Michigan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Michigan',
   'DATA'),
  ('pytz/zoneinfo/Canada/Eastern',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Macau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Algiers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Algiers',
   'DATA'),
  ('pytz/zoneinfo/Africa/Sao_Tome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Sao_Tome',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Port_Moresby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Port_Moresby',
   'DATA'),
  ('pytz/zoneinfo/Brazil/East',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/East',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Atyrau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Atyrau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Djibouti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Djibouti',
   'DATA'),
  ('pytz/zoneinfo/America/Winnipeg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Winnipeg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tel_Aviv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tel_Aviv',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+4',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+4',
   'DATA'),
  ('pytz/zoneinfo/Canada/Newfoundland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Newfoundland',
   'DATA'),
  ('pytz/zoneinfo/America/Montserrat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montserrat',
   'DATA'),
  ('pytz/zoneinfo/Asia/Magadan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Magadan',
   'DATA'),
  ('pytz/zoneinfo/Hongkong',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Hongkong',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+12',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+12',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashkhabad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ashkhabad',
   'DATA'),
  ('pytz/zoneinfo/Egypt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Egypt',
   'DATA'),
  ('pytz/zoneinfo/America/Thunder_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Thunder_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sofia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Sofia',
   'DATA'),
  ('pytz/zoneinfo/NZ',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/NZ',
   'DATA'),
  ('pytz/zoneinfo/Europe/Samara',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Samara',
   'DATA'),
  ('pytz/zoneinfo/America/Recife',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Recife',
   'DATA'),
  ('pytz/zoneinfo/Japan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Japan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+7',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+7',
   'DATA'),
  ('pytz/zoneinfo/Navajo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Navajo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Khandyga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Khandyga',
   'DATA'),
  ('pytz/zoneinfo/Asia/Srednekolymsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Srednekolymsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Douala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Douala',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belfast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Belfast',
   'DATA'),
  ('pytz/zoneinfo/Indian/Chagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Chagos',
   'DATA'),
  ('certifi/cacert.pem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/cacert.pem',
   'DATA'),
  ('certifi/py.typed',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/py.typed',
   'DATA'),
  ('Python3', 'Python3.framework/Versions/3.9/Python3', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/Documents/work/cursor-workspace/build/Excel数据处理工具/base_library.zip',
   'DATA'),
  ('QtCore', 'PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore', 'SYMLINK'),
  ('QtGui', 'PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui', 'SYMLINK'),
  ('QtDBus', 'PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus', 'SYMLINK'),
  ('QtQmlModels',
   'PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'SYMLINK'),
  ('QtQml', 'PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml', 'SYMLINK'),
  ('QtWebSockets',
   'PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'SYMLINK'),
  ('QtNetwork',
   'PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'SYMLINK'),
  ('QtQuick', 'PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick', 'SYMLINK'),
  ('QtWidgets',
   'PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'SYMLINK'),
  ('QtSvg', 'PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg', 'SYMLINK'),
  ('QtPrintSupport',
   'PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/QtPrintSupport',
   'Versions/Current/QtPrintSupport',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/QtQml', 'Versions/Current/QtQml', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQml.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/QtQmlModels',
   'Versions/Current/QtQmlModels',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/QtQuick',
   'Versions/Current/QtQuick',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/QtWebSockets',
   'Versions/Current/QtWebSockets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/Current', '5', 'SYMLINK'),
  ('Python3.framework/Python3', 'Versions/Current/Python3', 'SYMLINK'),
  ('Python3.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python3.framework/Versions/3.9/Resources/Info.plist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Resources/Info.plist',
   'DATA'),
  ('Python3.framework/Versions/Current', '3.9', 'SYMLINK')],
 'Python3',
 False,
 False,
 False,
 [],
 'arm64',
 None,
 None)
