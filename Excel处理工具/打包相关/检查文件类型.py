#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查可执行文件类型和兼容性
"""

import os
import platform
import struct

def check_file_type(file_path):
    """检查文件类型"""
    if not os.path.exists(file_path):
        return "文件不存在"
    
    try:
        with open(file_path, 'rb') as f:
            # 读取文件头
            header = f.read(16)
            
            if len(header) < 4:
                return "文件太小，无法识别"
            
            # 检查PE头（Windows可执行文件）
            if header[:2] == b'MZ':
                return "Windows PE可执行文件 (.exe)"
            
            # 检查ELF头（Linux可执行文件）
            elif header[:4] == b'\x7fELF':
                return "Linux ELF可执行文件"
            
            # 检查Mach-O头（macOS可执行文件）
            elif header[:4] in [b'\xfe\xed\xfa\xce', b'\xfe\xed\xfa\xcf', 
                               b'\xce\xfa\xed\xfe', b'\xcf\xfa\xed\xfe']:
                return "macOS Mach-O可执行文件"
            
            # 检查脚本文件
            elif header[:2] == b'#!':
                return "Shell脚本文件"
            
            else:
                return f"未知文件类型 (头部: {header[:8].hex()})"
                
    except Exception as e:
        return f"读取文件失败: {e}"

def main():
    print("=" * 60)
    print("           Excel数据处理工具 - 文件类型检查")
    print("=" * 60)
    print()
    
    # 检查当前系统
    current_system = platform.system()
    print(f"当前操作系统: {current_system}")
    print()
    
    # 检查可能的可执行文件
    possible_files = [
        "dist/Excel数据处理工具.exe",
        "dist/Excel数据处理工具",
        "Excel数据处理工具_分发包/Excel数据处理工具.exe",
        "Excel数据处理工具_分发包/Excel数据处理工具",
        "Excel数据处理工具_分发包 2/Excel数据处理工具"
    ]
    
    found_files = []
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            file_type = check_file_type(file_path)
            found_files.append((file_path, file_type))
            print(f"📁 {file_path}")
            print(f"   类型: {file_type}")
            
            # 兼容性检查
            if current_system == "Windows":
                if "Windows PE" in file_type:
                    print("   ✅ 与当前Windows系统兼容")
                else:
                    print("   ❌ 与当前Windows系统不兼容")
            elif current_system == "Darwin":  # macOS
                if "Mach-O" in file_type:
                    print("   ✅ 与当前macOS系统兼容")
                else:
                    print("   ❌ 与当前macOS系统不兼容")
            elif current_system == "Linux":
                if "ELF" in file_type:
                    print("   ✅ 与当前Linux系统兼容")
                else:
                    print("   ❌ 与当前Linux系统不兼容")
            
            print()
    
    if not found_files:
        print("❌ 未找到任何可执行文件")
        print()
        print("建议操作:")
        print("1. 运行打包脚本生成可执行文件")
        if current_system == "Windows":
            print("   双击: Windows打包.bat")
        else:
            print("   运行: python build.py")
        print("2. 或使用源码方式运行:")
        print("   双击: start.bat (Windows) 或 ./start.sh (macOS/Linux)")
    else:
        print("📋 总结:")
        compatible_files = [f for f, t in found_files if 
                          (current_system == "Windows" and "Windows PE" in t) or
                          (current_system == "Darwin" and "Mach-O" in t) or
                          (current_system == "Linux" and "ELF" in t)]
        
        if compatible_files:
            print(f"✅ 找到 {len(compatible_files)} 个兼容的可执行文件")
            for file_path in compatible_files:
                print(f"   可直接运行: {file_path}")
        else:
            print("❌ 没有找到与当前系统兼容的可执行文件")
            print()
            print("解决方案:")
            if current_system == "Windows":
                print("1. 在Windows系统上重新打包:")
                print("   双击运行: Windows打包.bat")
                print("2. 或使用源码方式:")
                print("   双击运行: install.bat 然后 start.bat")
            else:
                print("1. 重新打包:")
                print("   运行: python build.py")
                print("2. 或使用源码方式:")
                print("   运行: ./install.sh 然后 ./start.sh")
    
    print()
    print("=" * 60)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
