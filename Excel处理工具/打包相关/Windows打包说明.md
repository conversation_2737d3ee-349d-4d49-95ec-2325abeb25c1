# Windows系统打包说明

## ⚠️ 重要提示
当前的可执行文件是在macOS系统上生成的，无法在Windows上直接运行。
要在Windows上正常使用，需要在Windows系统上重新打包。

## 🖥️ Windows打包步骤

### 1. 环境准备
在Windows系统上安装Python和依赖：
```cmd
# 安装Python 3.7+
# 下载地址：https://www.python.org/downloads/

# 安装依赖
pip install pyinstaller
pip install -r requirements.txt
```

### 2. 执行打包
```cmd
# 方法1：使用自动打包脚本
python build.py

# 方法2：手动打包
pyinstaller --onefile --windowed --name="Excel数据处理工具" excel_pyqt5_app.py
```

### 3. 生成结果
成功后会在`dist/`目录下生成：
- `Excel数据处理工具.exe` ✅ Windows可执行文件

## 🔄 临时解决方案

如果暂时无法在Windows上重新打包，可以使用源码方式：

### 1. 安装Python环境
- 下载Python 3.7+：https://www.python.org/downloads/
- 安装时勾选"Add Python to PATH"

### 2. 安装依赖
双击运行`install.bat`或手动执行：
```cmd
pip install PyQt5==5.15.9 pandas==2.0.3 openpyxl==3.1.2 requests==2.31.0
```

### 3. 启动应用
双击运行`start.bat`或手动执行：
```cmd
python excel_pyqt5_app.py
```

## 📋 跨平台打包建议

### 为不同平台打包：
- **Windows**：在Windows系统上打包 → 生成`.exe`
- **macOS**：在macOS系统上打包 → 生成`.app`
- **Linux**：在Linux系统上打包 → 生成可执行文件

### 自动化打包方案：
可以使用GitHub Actions等CI/CD工具实现跨平台自动打包。

## 🛠️ 修复当前问题

### 立即可用的解决方案：
1. **推荐**：使用源码方式运行（install.bat + start.bat）
2. **最佳**：在Windows系统上重新执行`python build.py`生成真正的.exe文件

### 文件关联问题：
如果Windows提示选择打开方式，说明：
- 文件不是Windows可执行格式
- 需要重新在Windows上打包生成.exe文件

## 🚀 快速修复指南

### 方案A：源码运行（推荐，立即可用）
1. 将整个项目文件夹复制到Windows系统
2. 双击`install.bat`安装依赖
3. 双击`start.bat`启动应用

### 方案B：Windows重新打包（最佳）
1. 在Windows系统上安装Python
2. 运行`python build.py`
3. 获得真正的`.exe`文件
