@echo off
chcp 65001
echo ========================================
echo    Excel数据处理工具 - 环境安装
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境！
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过！
echo.

echo 正在安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 安装失败！请检查网络连接或尝试使用国内镜像：
    echo pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    pause
    exit /b 1
)

echo.
echo ========================================
echo    安装完成！
echo ========================================
echo.
echo 现在可以双击 start.bat 启动应用
echo 或者运行命令：python excel_pyqt5_app.py
echo.
pause
