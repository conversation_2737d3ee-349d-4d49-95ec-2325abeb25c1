#!/bin/bash

echo "========================================"
echo "   Excel数据处理工具 - 环境安装"
echo "========================================"
echo

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python3环境！"
    echo "请先安装Python 3.7或更高版本"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macOS安装命令：brew install python3"
    else
        echo "Ubuntu/Debian安装命令：sudo apt install python3 python3-pip"
        echo "CentOS/RHEL安装命令：sudo yum install python3 python3-pip"
    fi
    exit 1
fi

echo "Python环境检查通过！"
echo

echo "正在安装依赖包..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo
    echo "安装失败！请检查网络连接或尝试使用国内镜像："
    echo "pip3 install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/"
    exit 1
fi

echo
echo "========================================"
echo "   安装完成！"
echo "========================================"
echo
echo "现在可以运行 ./start.sh 启动应用"
echo "或者运行命令：python3 excel_pyqt5_app.py"
echo

# 给启动脚本添加执行权限
chmod +x start.sh
