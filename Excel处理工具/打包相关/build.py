#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False
    return True

def build_app():
    """打包应用"""
    print("正在打包应用...")

    # 检测操作系统
    import platform
    system = platform.system()
    print(f"检测到操作系统: {system}")

    # 打包命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 不显示控制台窗口
        "--name=Excel数据处理工具",      # 应用名称
        "--clean",                      # 清理临时文件
    ]

    # Windows特定配置
    if system == "Windows":
        cmd.extend([
            "--icon=app.ico",           # Windows图标（如果存在）
            "--version-file=version.txt" # 版本信息（如果存在）
        ])
        print("✅ 使用Windows特定配置")

    cmd.append("excel_pyqt5_app.py")   # 主程序文件

    try:
        subprocess.check_call(cmd)
        print("✅ 应用打包成功")

        # 显示生成的文件信息
        if system == "Windows":
            exe_path = "dist/Excel数据处理工具.exe"
            if os.path.exists(exe_path):
                print(f"✅ 生成Windows可执行文件: {exe_path}")
            else:
                print("⚠️  未找到.exe文件，请检查打包过程")
        else:
            print(f"✅ 生成{system}可执行文件")

        return True
    except subprocess.CalledProcessError:
        print("❌ 应用打包失败")
        return False

def create_distribution():
    """创建分发包"""
    print("正在创建分发包...")
    
    # 创建分发目录
    dist_dir = "Excel数据处理工具_分发包"
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    os.makedirs(dist_dir)
    
    # 复制文件
    files_to_copy = [
        ("dist/Excel数据处理工具.exe", "Excel数据处理工具.exe"),
        ("使用说明.md", "使用说明.md"),
        ("演示数据.xlsx", "演示数据.xlsx"),
        ("PyQt5版本使用说明.md", "详细使用说明.md")
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(dist_dir, dst))
            print(f"✅ 复制文件: {dst}")
        else:
            print(f"⚠️  文件不存在: {src}")
    
    # 创建README
    readme_content = """# Excel数据处理工具

## 快速开始
1. 双击"Excel数据处理工具.exe"启动应用
2. 参考"使用说明.md"了解详细功能
3. 可以使用"演示数据.xlsx"进行测试

## 系统要求
- Windows 7/8/10/11
- 无需安装Python环境

## 功能特点
- 支持4种数据处理方式
- AI智能公式生成
- 批量数据处理
- 结果一键导出

## 技术支持
如有问题请查看"详细使用说明.md"
"""
    
    with open(os.path.join(dist_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ 分发包创建完成: {dist_dir}")

def main():
    """主函数"""
    print("=" * 50)
    print("Excel数据处理工具 - 自动打包脚本")
    print("=" * 50)
    
    # 检查主程序文件
    if not os.path.exists("excel_pyqt5_app.py"):
        print("❌ 找不到主程序文件: excel_pyqt5_app.py")
        return
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return
    
    # 打包应用
    if not build_app():
        return
    
    # 创建分发包
    create_distribution()
    
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print("分发包位置: Excel数据处理工具_分发包/")
    print("可以将整个文件夹压缩后分发给用户")
    print("=" * 50)

if __name__ == "__main__":
    main()
