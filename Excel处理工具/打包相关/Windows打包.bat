@echo off
chcp 65001
echo ========================================
echo    Excel数据处理工具 - Windows打包
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境！
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    echo 安装时请勾选"Add Python to PATH"
    pause
    exit /b 1
)

echo Python环境检查通过！
echo.

echo 正在检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo PyInstaller安装失败！
        pause
        exit /b 1
    )
)

echo 正在检查项目依赖...
if not exist "requirements.txt" (
    echo 错误：找不到requirements.txt文件！
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo 正在安装项目依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo 依赖安装失败！请检查网络连接
    pause
    exit /b 1
)

echo.
echo 正在执行打包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

python build.py

if errorlevel 1 (
    echo.
    echo 打包失败！请检查错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo    打包完成！
echo ========================================
echo.

if exist "dist\Excel数据处理工具.exe" (
    echo ✅ 成功生成Windows可执行文件：
    echo    dist\Excel数据处理工具.exe
    echo.
    echo 📁 分发包位置：
    if exist "Excel数据处理工具_分发包" (
        echo    Excel数据处理工具_分发包\
    )
    echo.
    echo 🚀 现在可以：
    echo 1. 直接运行 dist\Excel数据处理工具.exe
    echo 2. 将分发包文件夹压缩后分享给其他用户
) else (
    echo ⚠️  未找到.exe文件，打包可能未完全成功
)

echo.
echo 按任意键退出...
pause >nul
