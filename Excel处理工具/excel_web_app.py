#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据处理工具 - Web版本
使用Flask创建Web界面，解决GUI显示问题
"""

from flask import Flask, render_template, request, jsonify, send_file
import pandas as pd
import json
import os
import tempfile
from werkzeug.utils import secure_filename
import logging

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = tempfile.gettempdir()

# 全局变量存储数据
current_df = None
current_filename = None

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """上传Excel文件"""
    global current_df, current_filename
    
    if 'file' not in request.files:
        return jsonify({'error': '没有选择文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'})
    
    if file and file.filename.lower().endswith(('.xlsx', '.xls')):
        try:
            # 读取Excel文件
            current_df = pd.read_excel(file)
            current_filename = secure_filename(file.filename)
            
            # 返回文件信息和列数据
            columns_info = []
            for col in current_df.columns:
                first_value = str(current_df.iloc[0][col]) if len(current_df) > 0 else ""
                columns_info.append({
                    'name': col,
                    'first_value': first_value[:50] + "..." if len(first_value) > 50 else first_value,
                    'full_value': first_value
                })
            
            return jsonify({
                'success': True,
                'filename': current_filename,
                'rows': len(current_df),
                'columns': len(current_df.columns),
                'columns_info': columns_info
            })
            
        except Exception as e:
            return jsonify({'error': f'读取文件失败: {str(e)}'})
    
    return jsonify({'error': '不支持的文件格式'})

@app.route('/process', methods=['POST'])
def process_data():
    """处理数据"""
    global current_df, current_filename
    
    if current_df is None:
        return jsonify({'error': '请先上传Excel文件'})
    
    try:
        data = request.json
        tasks = data.get('tasks', [])
        result_option = data.get('result_option', 'new_workbook')
        
        if not tasks:
            return jsonify({'error': '请至少配置一个处理任务'})
        
        # 创建结果数据框
        result_df = current_df.copy()
        
        # 处理每个任务
        for task in tasks:
            column = task['column']
            method = task['method']
            config = task['config']
            
            if column not in current_df.columns:
                continue
            
            # 处理每一行
            results = []
            for idx, row in current_df.iterrows():
                value = row[column]
                try:
                    result = process_single_value(value, method, config, column, idx)
                    results.append(result)
                except Exception as e:
                    results.append(None)
            
            # 添加结果列
            result_column_name = f"{column}_处理结果"
            result_df[result_column_name] = results
        
        # 保存结果
        output_filename = f"{os.path.splitext(current_filename)[0]}_处理结果.xlsx"
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], output_filename)
        result_df.to_excel(output_path, index=False)
        
        return jsonify({
            'success': True,
            'message': '处理完成',
            'download_url': f'/download/{output_filename}'
        })
        
    except Exception as e:
        return jsonify({'error': f'处理失败: {str(e)}'})

@app.route('/test_run', methods=['POST'])
def test_run():
    """试运行"""
    global current_df
    
    if current_df is None:
        return jsonify({'error': '请先上传Excel文件'})
    
    try:
        data = request.json
        tasks = data.get('tasks', [])
        
        if not tasks:
            return jsonify({'error': '请至少配置一个处理任务'})
        
        # 处理第一行
        results = []
        for task in tasks:
            column = task['column']
            method = task['method']
            config = task['config']
            
            if column not in current_df.columns:
                continue
            
            value = current_df.iloc[0][column]
            try:
                result = process_single_value(value, method, config, column, 0)
                results.append({
                    'column': column,
                    'original': str(value),
                    'result': str(result) if result is not None else '处理失败'
                })
            except Exception as e:
                results.append({
                    'column': column,
                    'original': str(value),
                    'result': f'错误: {str(e)}'
                })
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        return jsonify({'error': f'试运行失败: {str(e)}'})

@app.route('/download/<filename>')
def download_file(filename):
    """下载处理结果"""
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if os.path.exists(file_path):
        return send_file(file_path, as_attachment=True)
    return "文件不存在", 404

def process_single_value(value, method, config, column, row_idx):
    """处理单个值"""
    if method == "执行算式":
        return process_formula(value, config)
    elif method == "按字段取值":
        return process_json_field(value, config)
    elif method == "AI写公式":
        return process_ai_formula(config, column, row_idx)
    elif method == "AI自定义任务":
        return process_ai_custom(value, config)
    else:
        return None

def process_formula(value, formula):
    """执行算式"""
    try:
        x = float(value)
        formula_eval = formula.replace('x', str(x))
        result = eval(formula_eval)
        return result
    except:
        return None

def process_json_field(value, fields):
    """提取JSON字段"""
    try:
        if isinstance(value, str):
            data = json.loads(value)
        else:
            data = value
        
        field_list = fields.split('|')
        results = []
        for field in field_list:
            field = field.strip()
            if field in data:
                results.append(str(data[field]))
            else:
                results.append("")
        
        return "|".join(results) if len(results) > 1 else results[0] if results else ""
    except:
        return None

def process_ai_formula(description, column, row_idx):
    """AI生成公式（示例）"""
    return f"=A{row_idx+2}+B{row_idx+2}  # 基于: {description}"

def process_ai_custom(value, description):
    """AI自定义处理（示例）"""
    return f"AI处理结果: {description[:20]}... (原值: {str(value)[:20]}...)"

# 创建HTML模板
def create_html_template():
    """创建HTML模板"""
    html_content = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据处理工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .file-info { background-color: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .task-config { border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .button:hover { background-color: #0056b3; }
        .button.secondary { background-color: #6c757d; }
        .button.success { background-color: #28a745; }
        .button.warning { background-color: #ffc107; color: black; }
        .error { color: red; }
        .success { color: green; }
        .hidden { display: none; }
        .progress { width: 100%; height: 20px; background-color: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-bar { height: 100%; background-color: #007bff; transition: width 0.3s; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 100px; }
        input, select, textarea { padding: 5px; margin: 5px; border: 1px solid #ccc; border-radius: 3px; }
        .column-config { display: flex; align-items: center; gap: 10px; margin: 5px 0; padding: 10px; background: #f9f9f9; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Excel数据处理工具</h1>
            <p>支持算式执行、JSON字段提取、AI公式生成等功能</p>
        </div>

        <!-- 文件上传区域 -->
        <div class="section">
            <h3>📁 文件选择</h3>
            <input type="file" id="fileInput" accept=".xlsx,.xls" />
            <button class="button" onclick="uploadFile()">上传文件</button>
            <div id="fileInfo" class="file-info hidden"></div>
        </div>

        <!-- 处理选项 -->
        <div class="section">
            <h3>⚙️ 处理选项</h3>
            <label>
                <input type="radio" name="resultOption" value="new_workbook" checked> 新建工作簿
            </label>
            <label>
                <input type="radio" name="resultOption" value="append"> 追加到原文件
            </label>
        </div>

        <!-- 任务配置区域 -->
        <div class="section">
            <h3>🔧 任务配置</h3>
            <div id="taskConfig">
                <p>请先上传Excel文件</p>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="section">
            <button class="button warning" onclick="testRun()">🧪 试运行</button>
            <button class="button success" onclick="processData()">▶️ 开始处理</button>
        </div>

        <!-- 状态显示 -->
        <div class="section">
            <div class="progress hidden" id="progressBar">
                <div class="progress-bar" style="width: 0%"></div>
            </div>
            <div id="status">就绪</div>
        </div>

        <!-- 结果显示 -->
        <div id="results" class="section hidden">
            <h3>📋 处理结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        let currentData = null;

        function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            setStatus('正在上传文件...');
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentData = data;
                    showFileInfo(data);
                    createTaskConfig(data.columns_info);
                    setStatus('文件上传成功');
                } else {
                    setStatus(data.error, 'error');
                }
            })
            .catch(error => {
                setStatus('上传失败: ' + error, 'error');
            });
        }

        function showFileInfo(data) {
            const fileInfo = document.getElementById('fileInfo');
            fileInfo.innerHTML = `
                <strong>文件:</strong> ${data.filename}<br>
                <strong>行数:</strong> ${data.rows}<br>
                <strong>列数:</strong> ${data.columns}
            `;
            fileInfo.classList.remove('hidden');
        }

        function createTaskConfig(columns) {
            const taskConfig = document.getElementById('taskConfig');
            let html = '<h4>配置需要处理的列:</h4>';
            
            columns.forEach((col, index) => {
                html += `
                    <div class="column-config">
                        <input type="checkbox" id="col_${index}" onchange="toggleConfig(${index})">
                        <label for="col_${index}" style="width: 150px;"><strong>${col.name}</strong></label>
                        <span style="width: 200px; overflow: hidden;">${col.first_value}</span>
                        <select id="method_${index}" disabled>
                            <option value="">选择处理方式</option>
                            <option value="执行算式">执行算式</option>
                            <option value="按字段取值">按字段取值</option>
                            <option value="AI写公式">AI写公式</option>
                            <option value="AI自定义任务">AI自定义任务</option>
                        </select>
                        <input type="text" id="config_${index}" placeholder="配置详情" disabled style="width: 200px;">
                    </div>
                `;
            });
            
            taskConfig.innerHTML = html;
        }

        function toggleConfig(index) {
            const checkbox = document.getElementById(`col_${index}`);
            const method = document.getElementById(`method_${index}`);
            const config = document.getElementById(`config_${index}`);
            
            method.disabled = !checkbox.checked;
            config.disabled = !checkbox.checked;
        }

        function getSelectedTasks() {
            const tasks = [];
            if (!currentData) return tasks;
            
            currentData.columns_info.forEach((col, index) => {
                const checkbox = document.getElementById(`col_${index}`);
                if (checkbox.checked) {
                    const method = document.getElementById(`method_${index}`).value;
                    const config = document.getElementById(`config_${index}`).value;
                    
                    if (method && config) {
                        tasks.push({
                            column: col.name,
                            method: method,
                            config: config
                        });
                    }
                }
            });
            
            return tasks;
        }

        function testRun() {
            const tasks = getSelectedTasks();
            if (tasks.length === 0) {
                alert('请至少配置一个处理任务');
                return;
            }

            setStatus('正在试运行...');
            
            fetch('/test_run', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ tasks: tasks })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showTestResults(data.results);
                    setStatus('试运行完成');
                } else {
                    setStatus(data.error, 'error');
                }
            })
            .catch(error => {
                setStatus('试运行失败: ' + error, 'error');
            });
        }

        function showTestResults(results) {
            const resultDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            
            let html = '<h4>试运行结果（第一行数据）:</h4><table>';
            html += '<tr><th>列名</th><th>原始值</th><th>处理结果</th></tr>';
            
            results.forEach(result => {
                html += `<tr>
                    <td>${result.column}</td>
                    <td>${result.original}</td>
                    <td>${result.result}</td>
                </tr>`;
            });
            
            html += '</table>';
            resultContent.innerHTML = html;
            resultDiv.classList.remove('hidden');
        }

        function processData() {
            const tasks = getSelectedTasks();
            if (tasks.length === 0) {
                alert('请至少配置一个处理任务');
                return;
            }

            const resultOption = document.querySelector('input[name="resultOption"]:checked').value;
            
            setStatus('正在处理数据...');
            showProgress(true);
            
            fetch('/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    tasks: tasks,
                    result_option: resultOption
                })
            })
            .then(response => response.json())
            .then(data => {
                showProgress(false);
                if (data.success) {
                    setStatus('处理完成');
                    showDownloadLink(data.download_url);
                } else {
                    setStatus(data.error, 'error');
                }
            })
            .catch(error => {
                showProgress(false);
                setStatus('处理失败: ' + error, 'error');
            });
        }

        function showDownloadLink(url) {
            const resultDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = `
                <h4>处理完成!</h4>
                <p><a href="${url}" class="button success">📥 下载处理结果</a></p>
            `;
            resultDiv.classList.remove('hidden');
        }

        function setStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = type;
        }

        function showProgress(show) {
            const progressBar = document.getElementById('progressBar');
            if (show) {
                progressBar.classList.remove('hidden');
            } else {
                progressBar.classList.add('hidden');
            }
        }
    </script>
</body>
</html>
    '''
    
    # 创建templates目录
    templates_dir = 'templates'
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    
    # 写入HTML文件
    with open(os.path.join(templates_dir, 'index.html'), 'w', encoding='utf-8') as f:
        f.write(html_content)

if __name__ == '__main__':
    print("创建HTML模板...")
    create_html_template()
    
    print("启动Excel数据处理工具 - Web版")
    print("请在浏览器中访问: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
