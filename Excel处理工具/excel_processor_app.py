import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import logging
import os
from threading import Thread
import re

# 延迟导入重型模块
pandas = None
openpyxl = None
requests = None

def import_heavy_modules():
    """延迟导入重型模块"""
    global pandas, openpyxl, requests
    if pandas is None:
        print("正在导入pandas...")
        import pandas as pd
        pandas = pd
    if openpyxl is None:
        print("正在导入openpyxl...")
        import openpyxl
        globals()['openpyxl'] = openpyxl
        from openpyxl import Workbook, load_workbook
        from openpyxl.utils.dataframe import dataframe_to_rows
        globals()['Workbook'] = Workbook
        globals()['load_workbook'] = load_workbook
        globals()['dataframe_to_rows'] = dataframe_to_rows
    if requests is None:
        print("正在导入requests...")
        import requests
        globals()['requests'] = requests

class ExcelProcessorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel数据处理工具")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.df = None
        self.file_path = None
        self.task_configs = {}  # 存储每列的任务配置
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        self.create_widgets()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(file_frame, text="选择Excel文件", command=self.select_file).grid(row=0, column=0, padx=(0, 10))
        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Button(file_frame, text="读取Excel", command=self.read_excel).grid(row=0, column=2, padx=(10, 0))
        
        # 结果存储选项
        result_frame = ttk.LabelFrame(main_frame, text="结果存储", padding="5")
        result_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.result_option = tk.StringVar(value="append")
        ttk.Radiobutton(result_frame, text="表格后方空白的列", variable=self.result_option, value="append").grid(row=0, column=0, padx=(0, 20))
        ttk.Radiobutton(result_frame, text="新建工作簿", variable=self.result_option, value="new_workbook").grid(row=0, column=1)
        
        # 任务配置区域
        task_frame = ttk.LabelFrame(main_frame, text="任务配置", padding="5")
        task_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建滚动区域
        canvas = tk.Canvas(task_frame)
        scrollbar = ttk.Scrollbar(task_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置权重
        task_frame.columnconfigure(0, weight=1)
        task_frame.rowconfigure(0, weight=1)
        
        # 执行按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="试运行", command=self.test_run).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="开始执行", command=self.start_processing).grid(row=0, column=1)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='determinate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.grid(row=5, column=0, columnspan=2, pady=(5, 0))
        
        # 配置权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def select_file(self):
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if file_path:
            self.file_path = file_path
            self.file_label.config(text=os.path.basename(file_path))
    
    def read_excel(self):
        if not self.file_path:
            messagebox.showerror("错误", "请先选择Excel文件")
            return

        try:
            # 导入pandas
            import_heavy_modules()
            self.df = pandas.read_excel(self.file_path)
            self.create_task_config_ui()
            messagebox.showinfo("成功", f"成功读取Excel文件，共{len(self.df)}行，{len(self.df.columns)}列")
        except Exception as e:
            messagebox.showerror("错误", f"读取Excel文件失败：{str(e)}")

    def create_task_config_ui(self):
        # 清空之前的配置
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        self.task_configs = {}

        # 创建表头
        headers = ["表头", "第一行内容", "是否处理", "处理方式", "任务详情"]
        for i, header in enumerate(headers):
            label = ttk.Label(self.scrollable_frame, text=header, font=("Arial", 10, "bold"))
            label.grid(row=0, column=i, padx=5, pady=5, sticky=tk.W)

        # 为每列创建配置行
        for idx, column in enumerate(self.df.columns):
            row = idx + 1

            # 表头
            ttk.Label(self.scrollable_frame, text=str(column)).grid(row=row, column=0, padx=5, pady=2, sticky=tk.W)

            # 第一行内容（缩略显示）
            first_value = str(self.df.iloc[0][column]) if len(self.df) > 0 else ""
            display_value = first_value[:30] + "..." if len(first_value) > 30 else first_value

            content_button = ttk.Button(
                self.scrollable_frame,
                text=display_value,
                command=lambda col=column, val=first_value: self.show_full_content(col, val)
            )
            content_button.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)

            # 是否处理复选框
            process_var = tk.BooleanVar()
            process_check = ttk.Checkbutton(
                self.scrollable_frame,
                variable=process_var,
                command=lambda col=column: self.toggle_processing(col)
            )
            process_check.grid(row=row, column=2, padx=5, pady=2)

            # 处理方式下拉框
            method_var = tk.StringVar()
            method_combo = ttk.Combobox(
                self.scrollable_frame,
                textvariable=method_var,
                values=["执行算式", "按字段取值", "AI写公式", "AI自定义任务"],
                state="disabled",
                width=15
            )
            method_combo.grid(row=row, column=3, padx=5, pady=2)
            method_combo.bind("<<ComboboxSelected>>", lambda _event, col=column: self.update_task_detail(col))

            # 任务详情输入框
            detail_frame = ttk.Frame(self.scrollable_frame)
            detail_frame.grid(row=row, column=4, padx=5, pady=2, sticky=(tk.W, tk.E))

            detail_text = tk.Text(detail_frame, height=2, width=40, state="disabled")
            detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            detail_scrollbar = ttk.Scrollbar(detail_frame, orient="vertical", command=detail_text.yview)
            detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            detail_text.configure(yscrollcommand=detail_scrollbar.set)

            # 存储配置
            self.task_configs[column] = {
                'process_var': process_var,
                'method_var': method_var,
                'method_combo': method_combo,
                'detail_text': detail_text,
                'detail_frame': detail_frame
            }

        # 配置列权重
        self.scrollable_frame.columnconfigure(4, weight=1)

    def show_full_content(self, column, content):
        # 创建弹窗显示完整内容
        popup = tk.Toplevel(self.root)
        popup.title(f"列 '{column}' 的完整内容")
        popup.geometry("600x400")

        text_widget = scrolledtext.ScrolledText(popup, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)

        ttk.Button(popup, text="关闭", command=popup.destroy).pack(pady=5)

    def toggle_processing(self, column):
        config = self.task_configs[column]
        is_enabled = config['process_var'].get()

        # 启用或禁用相关控件
        state = "normal" if is_enabled else "disabled"
        config['method_combo'].config(state=state)
        config['detail_text'].config(state=state)

        if is_enabled:
            self.update_task_detail(column)
        else:
            config['detail_text'].delete(1.0, tk.END)

    def update_task_detail(self, column):
        config = self.task_configs[column]
        method = config['method_var'].get()

        # 根据处理方式显示不同的提示信息
        prompts = {
            "执行算式": "在此处编写算式，使用 x 代表表格内容，如 (x+2)*3",
            "按字段取值": "单元格内容必须是JSON，或者JSON字符串，在此处编写需提取内容的字段名，提取多个时使用|分割字段名，如：msg|name|value",
            "AI写公式": "在此处使用文字描述公式功能，AI 会自行编写Excel公式",
            "AI自定义任务": "在此处使用文字描述待执行的任务，注意：该方式，AI 会顺序执行每一行的单元格内容，耗时耗钱"
        }

        detail_text = config['detail_text']
        detail_text.config(state="normal")
        detail_text.delete(1.0, tk.END)
        if method in prompts:
            detail_text.insert(tk.END, prompts[method])
        detail_text.config(state="normal")

    def callAPI(self, prompt, model="qwen-72b", session_id=None):
        # 确保requests已导入
        import_heavy_modules()

        url = "http://za-aigc-platform.test.za.biz/bots/lytestrobot/lycommonpromptskill/execute"
        headers = {
            'Content-Type': 'application/json',
            'access-key': '20240618170943ZWCOVXFBYROERESODN'
        }

        data = {
            "model": model,
            "prompt": prompt,
            "sessionId": session_id
        }

        try:
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            if 'data' in result:
                return result['data']
            else:
                logging.warning(f"API response does not contain 'data' field. Full response: {result}")
                return ""
        except Exception as e:
            logging.error(f"API request failed: {str(e)}")
            return ""

    def process_formula(self, value, formula):
        """执行算式处理"""
        try:
            # 将值转换为数值
            x = float(value)
            # 替换公式中的x
            formula = formula.replace('x', str(x))
            # 安全执行算式
            result = eval(formula)
            return result
        except:
            return None

    def process_json_field(self, value, fields):
        """按字段取值处理"""
        try:
            # 尝试解析JSON
            if isinstance(value, str):
                data = json.loads(value)
            else:
                data = value

            # 提取字段
            field_list = fields.split('|')
            results = []
            for field in field_list:
                field = field.strip()
                if field in data:
                    results.append(str(data[field]))
                else:
                    results.append("")

            return "|".join(results) if len(results) > 1 else results[0] if results else ""
        except:
            return None

    def process_ai_formula(self, description, column_name, row_index):
        """AI写公式处理"""
        try:
            prompt = f"""请根据以下描述，为Excel生成一个公式。
描述：{description}
当前列名：{column_name}
当前行号：{row_index + 2}  # Excel行号从1开始，加上表头行

请只返回Excel公式，不要包含其他解释。公式应该以=开头。
例如：=A2+B2 或 =SUM(A2:C2) 等。"""

            formula = self.callAPI(prompt)
            return formula.strip() if formula else None
        except:
            return None

    def process_ai_custom(self, value, description):
        """AI自定义任务处理"""
        try:
            prompt = f"""请根据以下任务描述处理给定的内容：
任务描述：{description}
待处理内容：{value}

请直接返回处理结果，不要包含其他解释。"""

            result = self.callAPI(prompt)
            return result.strip() if result else None
        except:
            return None

    def test_run(self):
        """试运行功能"""
        if self.df is None:
            messagebox.showerror("错误", "请先读取Excel文件")
            return

        # 检查是否有需要处理的列
        processing_columns = []
        for column, config in self.task_configs.items():
            if config['process_var'].get():
                processing_columns.append(column)

        if not processing_columns:
            messagebox.showwarning("警告", "请至少选择一列进行处理")
            return

        # 处理第一行数据
        results = {}
        errors = []

        for column in processing_columns:
            config = self.task_configs[column]
            method = config['method_var'].get()
            detail = config['detail_text'].get(1.0, tk.END).strip()

            if not method:
                errors.append(f"列 '{column}' 未选择处理方式")
                continue

            if not detail:
                errors.append(f"列 '{column}' 未填写任务详情")
                continue

            # 获取第一行数据
            value = self.df.iloc[0][column]

            try:
                if method == "执行算式":
                    result = self.process_formula(value, detail)
                elif method == "按字段取值":
                    result = self.process_json_field(value, detail)
                elif method == "AI写公式":
                    result = self.process_ai_formula(detail, column, 0)
                elif method == "AI自定义任务":
                    result = self.process_ai_custom(value, detail)
                else:
                    result = None

                results[column] = result

            except Exception as e:
                errors.append(f"列 '{column}' 处理出错：{str(e)}")

        # 显示结果
        if errors:
            messagebox.showerror("试运行失败", "\n".join(errors))
        else:
            self.show_test_results(results)

    def show_test_results(self, results):
        """显示试运行结果"""
        popup = tk.Toplevel(self.root)
        popup.title("试运行结果")
        popup.geometry("600x400")

        text_widget = scrolledtext.ScrolledText(popup, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget.insert(tk.END, "试运行结果（第一行数据处理结果）：\n\n")

        for column, result in results.items():
            text_widget.insert(tk.END, f"列 '{column}'：\n")
            text_widget.insert(tk.END, f"结果：{result if result is not None else '处理失败（空值）'}\n\n")

        text_widget.config(state=tk.DISABLED)

        button_frame = ttk.Frame(popup)
        button_frame.pack(pady=5)

        ttk.Button(button_frame, text="确认", command=popup.destroy).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="继续执行", command=lambda: [popup.destroy(), self.start_processing()]).pack(side=tk.LEFT, padx=5)

    def start_processing(self):
        """开始执行处理"""
        if self.df is None:
            messagebox.showerror("错误", "请先读取Excel文件")
            return

        # 检查是否有需要处理的列
        processing_columns = []
        for column, config in self.task_configs.items():
            if config['process_var'].get():
                processing_columns.append(column)

        if not processing_columns:
            messagebox.showwarning("警告", "请至少选择一列进行处理")
            return

        # 在新线程中执行处理，避免界面卡死
        thread = Thread(target=self.process_data, args=(processing_columns,))
        thread.daemon = True
        thread.start()

    def process_data(self, processing_columns):
        """处理数据的主函数"""
        try:
            self.root.after(0, lambda: self.status_label.config(text="正在处理数据..."))
            self.root.after(0, lambda: self.progress.config(maximum=len(self.df)))

            # 创建结果数据框
            result_df = self.df.copy()

            # 处理每一行
            for row_idx in range(len(self.df)):
                self.root.after(0, lambda idx=row_idx: self.progress.config(value=idx))
                self.root.after(0, lambda idx=row_idx: self.status_label.config(text=f"正在处理第 {idx+1}/{len(self.df)} 行"))

                for column in processing_columns:
                    config = self.task_configs[column]
                    method = config['method_var'].get()
                    detail = config['detail_text'].get(1.0, tk.END).strip()

                    # 获取当前行数据
                    value = self.df.iloc[row_idx][column]

                    try:
                        if method == "执行算式":
                            result = self.process_formula(value, detail)
                        elif method == "按字段取值":
                            result = self.process_json_field(value, detail)
                        elif method == "AI写公式":
                            result = self.process_ai_formula(detail, column, row_idx)
                        elif method == "AI自定义任务":
                            result = self.process_ai_custom(value, detail)
                        else:
                            result = None

                        # 创建新列名
                        new_column_name = f"{column}_处理结果"
                        result_df.loc[row_idx, new_column_name] = result

                    except Exception as e:
                        logging.error(f"处理第{row_idx+1}行，列'{column}'时出错：{str(e)}")
                        new_column_name = f"{column}_处理结果"
                        result_df.loc[row_idx, new_column_name] = None

            # 保存结果
            self.save_results(result_df)

            self.root.after(0, lambda: self.progress.config(value=len(self.df)))
            self.root.after(0, lambda: self.status_label.config(text="处理完成"))
            self.root.after(0, lambda: messagebox.showinfo("完成", "数据处理完成"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中出错：{str(e)}"))
            self.root.after(0, lambda: self.status_label.config(text="处理失败"))

    def save_results(self, result_df):
        """保存处理结果"""
        try:
            # 确保模块已导入
            import_heavy_modules()

            if self.result_option.get() == "append":
                # 保存到原文件的后方列
                with pandas.ExcelWriter(self.file_path, engine='openpyxl', mode='a', if_sheet_exists='overlay') as writer:
                    result_df.to_excel(writer, index=False, sheet_name='Sheet1')
            else:
                # 新建工作簿
                base_name = os.path.splitext(self.file_path)[0]
                new_file_path = f"{base_name}_处理结果.xlsx"
                result_df.to_excel(new_file_path, index=False)
                self.root.after(0, lambda: messagebox.showinfo("保存完成", f"结果已保存到：{new_file_path}"))

        except Exception as e:
            logging.error(f"保存结果时出错：{str(e)}")
            # 尝试使用openpyxl直接保存
            try:
                base_name = os.path.splitext(self.file_path)[0]
                new_file_path = f"{base_name}_处理结果.xlsx"

                from openpyxl import Workbook
                from openpyxl.utils.dataframe import dataframe_to_rows

                wb = Workbook()
                ws = wb.active

                # 写入数据
                for r in dataframe_to_rows(result_df, index=False, header=True):
                    ws.append(r)

                wb.save(new_file_path)
                self.root.after(0, lambda: messagebox.showinfo("保存完成", f"结果已保存到：{new_file_path}"))

            except Exception as e2:
                self.root.after(0, lambda: messagebox.showerror("保存失败", f"保存结果时出错：{str(e2)}"))


def main():
    print("正在启动Excel数据处理工具...")
    try:
        root = tk.Tk()
        print("Tkinter窗口创建成功")
        app = ExcelProcessorApp(root)
        print("应用初始化完成，启动主循环...")
        root.mainloop()
        print("应用已关闭")
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
