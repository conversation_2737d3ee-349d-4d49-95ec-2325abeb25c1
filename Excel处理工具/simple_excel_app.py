#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Excel处理应用 - 用于调试
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import traceback

def test_tkinter():
    """测试Tkinter是否正常工作"""
    try:
        print("正在创建Tkinter窗口...")
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        # 添加一个简单的标签
        label = ttk.Label(root, text="如果您看到这个窗口，说明Tkinter工作正常！")
        label.pack(pady=50)
        
        # 添加一个按钮
        button = ttk.Button(root, text="点击测试", command=lambda: messagebox.showinfo("测试", "按钮工作正常！"))
        button.pack(pady=10)
        
        print("窗口创建成功，正在显示...")
        root.mainloop()
        print("窗口已关闭")
        
    except Exception as e:
        print(f"Tkinter测试失败: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    print("开始启动简化版Excel应用...")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {sys.path[0]}")
    
    try:
        # 测试导入
        print("测试导入模块...")
        import pandas as pd
        print("✓ pandas导入成功")
        
        import openpyxl
        print("✓ openpyxl导入成功")
        
        import requests
        print("✓ requests导入成功")
        
        # 测试Tkinter
        print("测试Tkinter...")
        test_tkinter()
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        return
    except Exception as e:
        print(f"启动失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
