# Excel数据处理工具 - 问题排查指南

## 常见问题及解决方案

### 1. 应用启动后页面空白

**可能原因：**
- Tkinter初始化问题
- 模块导入延迟
- 系统兼容性问题

**解决方案：**
```bash
# 方法1：使用启动脚本
python3 启动应用.py

# 方法2：直接启动
python3 excel_processor_app.py

# 方法3：设置环境变量后启动
export TK_SILENCE_DEPRECATION=1
python3 excel_processor_app.py
```

### 2. 模块导入错误

**错误信息：** `ModuleNotFoundError: No module named 'pandas'`

**解决方案：**
```bash
# 安装必要依赖
python3 -m pip install pandas openpyxl requests

# 或者使用pip
pip3 install pandas openpyxl requests
```

### 3. Excel文件读取失败

**可能原因：**
- 文件格式不支持
- 文件损坏
- 权限问题

**解决方案：**
- 确保文件是.xlsx或.xls格式
- 检查文件是否被其他程序占用
- 尝试用Excel重新保存文件

### 4. AI功能无法使用

**可能原因：**
- 网络连接问题
- API密钥错误
- 服务器不可达

**解决方案：**
- 检查网络连接
- 确认API接口地址正确
- 联系管理员确认API服务状态

### 5. 处理结果为空

**可能原因：**
- 算式语法错误
- JSON格式不正确
- 字段名不存在

**解决方案：**
- 使用"试运行"功能验证配置
- 检查算式语法（如：x+1, x*2）
- 确认JSON数据格式正确
- 验证字段名是否存在

### 6. 保存结果失败

**可能原因：**
- 目标文件被占用
- 磁盘空间不足
- 权限不足

**解决方案：**
- 关闭Excel等可能占用文件的程序
- 检查磁盘空间
- 选择"新建工作簿"选项

## 性能优化建议

### 1. 大文件处理
- 分批处理大量数据
- 避免同时处理多个AI任务
- 关闭不必要的程序释放内存

### 2. AI功能优化
- 合理使用AI功能，避免过度调用
- 优先使用"执行算式"和"按字段取值"
- AI任务描述要清晰具体

### 3. 系统要求
- Python 3.7+
- 至少2GB可用内存
- 稳定的网络连接（AI功能）

## 调试模式

如果遇到问题，可以启用调试模式：

```python
# 在excel_processor_app.py开头添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 操作系统版本
2. Python版本
3. 错误信息截图
4. 操作步骤描述

## 版本信息

- 应用版本：1.0
- 支持的Excel格式：.xlsx, .xls
- Python要求：3.7+
- 依赖库：pandas, openpyxl, requests, tkinter
