#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据处理工具 - 简化版本
确保界面正常显示
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import json
import logging
from threading import Thread

class ExcelProcessorV2:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel数据处理工具 v2.0")
        self.root.geometry("1000x700")
        
        # 数据存储
        self.df = None
        self.file_path = None
        self.task_configs = {}
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        # 立即创建界面
        self.create_interface()
        
        print("界面创建完成")
    
    def create_interface(self):
        """创建用户界面"""
        print("开始创建界面...")
        
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_container, text="Excel数据处理工具", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 文件选择区域
        self.create_file_section(main_container)
        
        # 结果存储选项
        self.create_result_section(main_container)
        
        # 任务配置区域
        self.create_task_section(main_container)
        
        # 按钮区域
        self.create_button_section(main_container)
        
        # 状态区域
        self.create_status_section(main_container)
        
        print("所有界面组件创建完成")
    
    def create_file_section(self, parent):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(parent, text="📁 文件选择", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件路径显示
        self.file_var = tk.StringVar(value="未选择文件")
        file_label = ttk.Label(file_frame, textvariable=self.file_var, 
                              foreground="gray")
        file_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 按钮
        btn_frame = ttk.Frame(file_frame)
        btn_frame.pack(side=tk.RIGHT)
        
        ttk.Button(btn_frame, text="选择Excel文件", 
                  command=self.select_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="读取Excel", 
                  command=self.read_excel).pack(side=tk.LEFT)
    
    def create_result_section(self, parent):
        """创建结果存储选项"""
        result_frame = ttk.LabelFrame(parent, text="💾 结果存储", padding=10)
        result_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.result_option = tk.StringVar(value="append")
        ttk.Radiobutton(result_frame, text="表格后方空白的列", 
                       variable=self.result_option, value="append").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(result_frame, text="新建工作簿", 
                       variable=self.result_option, value="new_workbook").pack(side=tk.LEFT)
    
    def create_task_section(self, parent):
        """创建任务配置区域"""
        task_frame = ttk.LabelFrame(parent, text="⚙️ 任务配置", padding=10)
        task_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 说明文本
        info_text = "请先选择并读取Excel文件，然后配置需要处理的列"
        self.info_label = ttk.Label(task_frame, text=info_text, 
                                   foreground="blue")
        self.info_label.pack(pady=10)
        
        # 滚动区域
        canvas = tk.Canvas(task_frame, height=200)
        scrollbar = ttk.Scrollbar(task_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 示例配置（初始状态）
        self.show_example_config()
    
    def show_example_config(self):
        """显示示例配置"""
        example_frame = ttk.Frame(self.scrollable_frame)
        example_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(example_frame, text="示例列", width=15, 
                 background="lightgray").pack(side=tk.LEFT, padx=2)
        ttk.Label(example_frame, text="示例数据内容...", width=25, 
                 background="lightgray").pack(side=tk.LEFT, padx=2)
        ttk.Checkbutton(example_frame, text="处理", state="disabled").pack(side=tk.LEFT, padx=2)
        
        method_combo = ttk.Combobox(example_frame, 
                                   values=["执行算式", "按字段取值", "AI写公式", "AI自定义任务"], 
                                   width=12, state="disabled")
        method_combo.pack(side=tk.LEFT, padx=2)
        
        ttk.Entry(example_frame, width=30, state="disabled").pack(side=tk.LEFT, padx=2)
    
    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="🧪 试运行", 
                  command=self.test_run).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="▶️ 开始执行", 
                  command=self.start_processing).pack(side=tk.LEFT)
        
        # 右侧帮助按钮
        ttk.Button(button_frame, text="❓ 帮助", 
                  command=self.show_help).pack(side=tk.RIGHT)
    
    def create_status_section(self, parent):
        """创建状态区域"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='determinate')
        self.progress.pack(fill=tk.X, pady=(0, 5))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪 - 请选择Excel文件开始")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack()
    
    def select_file(self):
        """选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path = file_path
            self.file_var.set(os.path.basename(file_path))
            self.status_var.set(f"已选择文件: {os.path.basename(file_path)}")
    
    def read_excel(self):
        """读取Excel文件"""
        if not self.file_path:
            messagebox.showerror("错误", "请先选择Excel文件")
            return
        
        try:
            self.status_var.set("正在读取Excel文件...")
            self.root.update()
            
            # 延迟导入pandas
            import pandas as pd
            self.df = pd.read_excel(self.file_path)
            
            # 清空示例配置
            for widget in self.scrollable_frame.winfo_children():
                widget.destroy()
            
            # 创建实际配置
            self.create_column_configs()
            
            self.status_var.set(f"成功读取 {len(self.df)} 行 {len(self.df.columns)} 列数据")
            self.info_label.config(text="配置完成后，可以使用试运行验证设置")
            
            messagebox.showinfo("成功", f"成功读取Excel文件\n行数: {len(self.df)}\n列数: {len(self.df.columns)}")
            
        except Exception as e:
            self.status_var.set("读取失败")
            messagebox.showerror("错误", f"读取Excel文件失败:\n{str(e)}")
    
    def create_column_configs(self):
        """创建列配置界面"""
        # 表头
        header_frame = ttk.Frame(self.scrollable_frame)
        header_frame.pack(fill=tk.X, pady=(0, 5))
        
        headers = ["列名", "第一行内容", "处理", "处理方式", "任务详情"]
        widths = [15, 25, 8, 12, 30]
        
        for i, (header, width) in enumerate(zip(headers, widths)):
            ttk.Label(header_frame, text=header, width=width, 
                     font=("Arial", 9, "bold")).pack(side=tk.LEFT, padx=2)
        
        # 为每列创建配置
        self.task_configs = {}
        for idx, column in enumerate(self.df.columns):
            self.create_column_config(column, idx)
    
    def create_column_config(self, column, idx):
        """创建单列配置"""
        config_frame = ttk.Frame(self.scrollable_frame)
        config_frame.pack(fill=tk.X, pady=2)
        
        # 列名
        ttk.Label(config_frame, text=str(column)[:15], width=15).pack(side=tk.LEFT, padx=2)
        
        # 第一行内容
        first_value = str(self.df.iloc[0][column]) if len(self.df) > 0 else ""
        display_value = first_value[:25] + "..." if len(first_value) > 25 else first_value
        
        content_btn = ttk.Button(config_frame, text=display_value, width=25,
                                command=lambda: self.show_content(column, first_value))
        content_btn.pack(side=tk.LEFT, padx=2)
        
        # 处理复选框
        process_var = tk.BooleanVar()
        process_check = ttk.Checkbutton(config_frame, variable=process_var,
                                       command=lambda: self.toggle_config(column))
        process_check.pack(side=tk.LEFT, padx=2)
        
        # 处理方式
        method_var = tk.StringVar()
        method_combo = ttk.Combobox(config_frame, textvariable=method_var,
                                   values=["执行算式", "按字段取值", "AI写公式", "AI自定义任务"],
                                   width=12, state="disabled")
        method_combo.pack(side=tk.LEFT, padx=2)
        
        # 任务详情
        detail_var = tk.StringVar()
        detail_entry = ttk.Entry(config_frame, textvariable=detail_var, 
                                width=30, state="disabled")
        detail_entry.pack(side=tk.LEFT, padx=2)
        
        # 存储配置
        self.task_configs[column] = {
            'process_var': process_var,
            'method_var': method_var,
            'method_combo': method_combo,
            'detail_var': detail_var,
            'detail_entry': detail_entry
        }
    
    def show_content(self, column, content):
        """显示完整内容"""
        popup = tk.Toplevel(self.root)
        popup.title(f"列 '{column}' 的内容")
        popup.geometry("500x300")
        
        text_widget = scrolledtext.ScrolledText(popup)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
        
        ttk.Button(popup, text="关闭", command=popup.destroy).pack(pady=5)
    
    def toggle_config(self, column):
        """切换配置状态"""
        config = self.task_configs[column]
        enabled = config['process_var'].get()
        
        state = "normal" if enabled else "disabled"
        config['method_combo'].config(state=state)
        config['detail_entry'].config(state=state)
    
    def test_run(self):
        """试运行"""
        if self.df is None:
            messagebox.showerror("错误", "请先读取Excel文件")
            return

        # 检查配置
        processing_columns = []
        for column, config in self.task_configs.items():
            if config['process_var'].get():
                processing_columns.append(column)

        if not processing_columns:
            messagebox.showwarning("警告", "请至少选择一列进行处理")
            return

        # 处理第一行
        results = {}
        errors = []

        for column in processing_columns:
            config = self.task_configs[column]
            method = config['method_var'].get()
            detail = config['detail_var'].get().strip()

            if not method:
                errors.append(f"列 '{column}' 未选择处理方式")
                continue

            if not detail:
                errors.append(f"列 '{column}' 未填写任务详情")
                continue

            # 获取第一行数据
            value = self.df.iloc[0][column]

            try:
                result = self.process_single_value(value, method, detail, column, 0)
                results[column] = result
            except Exception as e:
                errors.append(f"列 '{column}' 处理出错：{str(e)}")

        # 显示结果
        if errors:
            messagebox.showerror("试运行失败", "\n".join(errors))
        else:
            self.show_test_results(results)

    def process_single_value(self, value, method, detail, column, row_idx):
        """处理单个值"""
        if method == "执行算式":
            return self.process_formula(value, detail)
        elif method == "按字段取值":
            return self.process_json_field(value, detail)
        elif method == "AI写公式":
            return self.process_ai_formula(detail, column, row_idx)
        elif method == "AI自定义任务":
            return self.process_ai_custom(value, detail)
        else:
            return None

    def process_formula(self, value, formula):
        """执行算式"""
        try:
            x = float(value)
            formula_eval = formula.replace('x', str(x))
            result = eval(formula_eval)
            return result
        except:
            return None

    def process_json_field(self, value, fields):
        """提取JSON字段"""
        try:
            if isinstance(value, str):
                data = json.loads(value)
            else:
                data = value

            field_list = fields.split('|')
            results = []
            for field in field_list:
                field = field.strip()
                if field in data:
                    results.append(str(data[field]))
                else:
                    results.append("")

            return "|".join(results) if len(results) > 1 else results[0] if results else ""
        except:
            return None

    def process_ai_formula(self, description, column, row_idx):
        """AI生成公式"""
        # 这里可以集成实际的AI API
        return f"=A{row_idx+2}+B{row_idx+2}  # 示例公式"

    def process_ai_custom(self, value, description):
        """AI自定义处理"""
        # 这里可以集成实际的AI API
        return f"AI处理结果: {description[:20]}..."

    def show_test_results(self, results):
        """显示试运行结果"""
        popup = tk.Toplevel(self.root)
        popup.title("试运行结果")
        popup.geometry("600x400")

        text_widget = scrolledtext.ScrolledText(popup)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget.insert(tk.END, "试运行结果（第一行数据处理结果）：\n\n")

        for column, result in results.items():
            text_widget.insert(tk.END, f"列 '{column}'：\n")
            text_widget.insert(tk.END, f"结果：{result if result is not None else '处理失败（空值）'}\n\n")

        text_widget.config(state=tk.DISABLED)

        button_frame = ttk.Frame(popup)
        button_frame.pack(pady=5)

        ttk.Button(button_frame, text="确认", command=popup.destroy).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="继续执行",
                  command=lambda: [popup.destroy(), self.start_processing()]).pack(side=tk.LEFT, padx=5)

    def start_processing(self):
        """开始处理"""
        if self.df is None:
            messagebox.showerror("错误", "请先读取Excel文件")
            return

        # 检查配置
        processing_columns = []
        for column, config in self.task_configs.items():
            if config['process_var'].get():
                processing_columns.append(column)

        if not processing_columns:
            messagebox.showwarning("警告", "请至少选择一列进行处理")
            return

        # 在新线程中处理
        thread = Thread(target=self.process_data, args=(processing_columns,))
        thread.daemon = True
        thread.start()

    def process_data(self, processing_columns):
        """处理数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在处理数据..."))
            self.root.after(0, lambda: self.progress.config(maximum=len(self.df)))

            # 导入pandas
            import pandas as pd
            result_df = self.df.copy()

            # 处理每一行
            for row_idx in range(len(self.df)):
                self.root.after(0, lambda idx=row_idx: self.progress.config(value=idx))
                self.root.after(0, lambda idx=row_idx: self.status_var.set(f"正在处理第 {idx+1}/{len(self.df)} 行"))

                for column in processing_columns:
                    config = self.task_configs[column]
                    method = config['method_var'].get()
                    detail = config['detail_var'].get().strip()

                    value = self.df.iloc[row_idx][column]

                    try:
                        result = self.process_single_value(value, method, detail, column, row_idx)
                        new_column_name = f"{column}_处理结果"
                        result_df.loc[row_idx, new_column_name] = result
                    except Exception as e:
                        logging.error(f"处理第{row_idx+1}行，列'{column}'时出错：{str(e)}")
                        new_column_name = f"{column}_处理结果"
                        result_df.loc[row_idx, new_column_name] = None

            # 保存结果
            self.save_results(result_df)

            self.root.after(0, lambda: self.progress.config(value=len(self.df)))
            self.root.after(0, lambda: self.status_var.set("处理完成"))
            self.root.after(0, lambda: messagebox.showinfo("完成", "数据处理完成"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中出错：{str(e)}"))
            self.root.after(0, lambda: self.status_var.set("处理失败"))

    def save_results(self, result_df):
        """保存结果"""
        try:
            base_name = os.path.splitext(self.file_path)[0]
            new_file_path = f"{base_name}_处理结果.xlsx"
            result_df.to_excel(new_file_path, index=False)
            self.root.after(0, lambda: messagebox.showinfo("保存完成", f"结果已保存到：{new_file_path}"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("保存失败", f"保存结果时出错：{str(e)}"))
    
    def show_help(self):
        """显示帮助"""
        help_text = """Excel数据处理工具使用说明：

1. 选择Excel文件并读取
2. 配置需要处理的列：
   - 勾选"处理"复选框
   - 选择处理方式
   - 填写任务详情
3. 使用试运行验证配置
4. 开始执行批量处理

处理方式说明：
• 执行算式：数学运算，用x代表单元格值
• 按字段取值：从JSON中提取字段
• AI写公式：生成Excel公式
• AI自定义任务：智能文本处理"""
        
        messagebox.showinfo("帮助", help_text)


def main():
    print("启动Excel数据处理工具 v2.0...")
    
    # 设置环境变量
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    
    try:
        root = tk.Tk()
        print("创建主窗口...")
        
        app = ExcelProcessorV2(root)
        print("应用初始化完成")
        
        # 确保窗口显示
        root.update()
        root.deiconify()  # 确保窗口可见
        root.lift()       # 提升窗口
        root.focus_force() # 强制获得焦点
        
        print("启动主循环...")
        root.mainloop()
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
