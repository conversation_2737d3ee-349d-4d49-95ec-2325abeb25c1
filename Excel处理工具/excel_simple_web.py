#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据处理工具 - 简单Web版本
使用Python内置模块，无需额外依赖
"""

import http.server
import socketserver
import json
import os
import urllib.parse
import pandas as pd
import tempfile
from io import BytesIO
import base64

class ExcelHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_html_page()
        else:
            super().do_GET()
    
    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        if self.path == '/upload':
            self.handle_upload(post_data)
        elif self.path == '/process':
            self.handle_process(post_data)
        else:
            self.send_error(404)
    
    def send_html_page(self):
        """发送HTML页面"""
        html_content = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据处理工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .button:hover { background-color: #0056b3; }
        .button.success { background-color: #28a745; }
        .button.warning { background-color: #ffc107; color: black; }
        .error { color: red; }
        .success { color: green; }
        .hidden { display: none; }
        .file-info { background-color: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .task-item { border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px; background: #f9f9f9; }
        .form-group { margin: 10px 0; }
        input, select, textarea { padding: 5px; margin: 5px; border: 1px solid #ccc; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Excel数据处理工具</h1>
            <p>支持算式执行、JSON字段提取、AI公式生成等功能</p>
        </div>

        <!-- 文件上传区域 -->
        <div class="section">
            <h3>📁 文件选择</h3>
            <input type="file" id="fileInput" accept=".xlsx,.xls" />
            <button class="button" onclick="handleFile()">读取文件</button>
            <div id="fileInfo" class="file-info hidden"></div>
        </div>

        <!-- 任务配置区域 -->
        <div class="section">
            <h3>🔧 任务配置</h3>
            <div id="taskConfig">
                <p>请先选择并读取Excel文件</p>
            </div>
        </div>

        <!-- 示例配置 -->
        <div class="section">
            <h3>📋 使用示例</h3>
            <div class="task-item">
                <h4>1. 执行算式</h4>
                <p><strong>用途：</strong>对数值列进行数学运算</p>
                <p><strong>配置示例：</strong>(x+2)*3 或 x/100</p>
                <p><strong>说明：</strong>使用 x 代表当前单元格的值</p>
            </div>
            <div class="task-item">
                <h4>2. 按字段取值</h4>
                <p><strong>用途：</strong>从JSON数据中提取特定字段</p>
                <p><strong>配置示例：</strong>name|age|city</p>
                <p><strong>说明：</strong>使用 | 分隔多个字段名</p>
            </div>
            <div class="task-item">
                <h4>3. AI写公式</h4>
                <p><strong>用途：</strong>让AI生成Excel公式</p>
                <p><strong>配置示例：</strong>计算A列和B列的和</p>
                <p><strong>说明：</strong>用自然语言描述需要的公式功能</p>
            </div>
            <div class="task-item">
                <h4>4. AI自定义任务</h4>
                <p><strong>用途：</strong>让AI处理复杂的文本任务</p>
                <p><strong>配置示例：</strong>提取文本中的关键信息并总结</p>
                <p><strong>说明：</strong>会逐行调用AI处理，耗时较长</p>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="section">
            <button class="button warning" onclick="testProcess()">🧪 试运行</button>
            <button class="button success" onclick="processAll()">▶️ 开始处理</button>
        </div>

        <!-- 状态显示 -->
        <div class="section">
            <div id="status">就绪 - 请选择Excel文件开始</div>
        </div>

        <!-- 结果显示 -->
        <div id="results" class="section hidden">
            <h3>📋 处理结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        let currentData = null;
        let currentColumns = [];

        function handleFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择文件');
                return;
            }

            if (!file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls')) {
                alert('请选择Excel文件(.xlsx或.xls)');
                return;
            }

            setStatus('正在读取文件...');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    // 这里简化处理，实际应用中需要使用专门的Excel解析库
                    showFileInfo(file.name);
                    createMockTaskConfig();
                    setStatus('文件读取成功（演示模式）');
                } catch (error) {
                    setStatus('文件读取失败: ' + error, 'error');
                }
            };
            
            reader.readAsArrayBuffer(file);
        }

        function showFileInfo(filename) {
            const fileInfo = document.getElementById('fileInfo');
            fileInfo.innerHTML = `
                <strong>文件:</strong> ${filename}<br>
                <strong>状态:</strong> 已读取（演示模式）<br>
                <strong>说明:</strong> 这是演示版本，实际功能需要完整的服务器环境
            `;
            fileInfo.classList.remove('hidden');
        }

        function createMockTaskConfig() {
            const taskConfig = document.getElementById('taskConfig');
            const mockColumns = ['数值列', 'JSON数据', '文本内容', '时间数据'];
            
            let html = '<h4>配置需要处理的列:</h4>';
            html += '<p style="color: #666;">以下是演示配置，实际使用时会显示您Excel文件中的真实列名</p>';
            
            mockColumns.forEach((col, index) => {
                html += `
                    <div class="task-item">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <input type="checkbox" id="col_${index}">
                            <label for="col_${index}" style="font-weight: bold; width: 120px;">${col}</label>
                            <span style="color: #666;">示例数据...</span>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <select id="method_${index}" style="width: 150px;">
                                <option value="">选择处理方式</option>
                                <option value="执行算式">执行算式</option>
                                <option value="按字段取值">按字段取值</option>
                                <option value="AI写公式">AI写公式</option>
                                <option value="AI自定义任务">AI自定义任务</option>
                            </select>
                            <input type="text" id="config_${index}" placeholder="配置详情" style="flex: 1;">
                        </div>
                    </div>
                `;
            });
            
            taskConfig.innerHTML = html;
        }

        function testProcess() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                alert('请至少配置一个处理任务');
                return;
            }

            setStatus('正在试运行...');
            
            // 模拟处理结果
            setTimeout(() => {
                const mockResults = selectedTasks.map(task => ({
                    column: task.column,
                    method: task.method,
                    config: task.config,
                    result: `模拟结果: ${task.method} - ${task.config}`
                }));
                
                showTestResults(mockResults);
                setStatus('试运行完成（演示模式）');
            }, 1000);
        }

        function processAll() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                alert('请至少配置一个处理任务');
                return;
            }

            setStatus('正在处理数据...');
            
            // 模拟处理过程
            setTimeout(() => {
                showProcessResults();
                setStatus('处理完成（演示模式）');
            }, 2000);
        }

        function getSelectedTasks() {
            const tasks = [];
            const mockColumns = ['数值列', 'JSON数据', '文本内容', '时间数据'];
            
            mockColumns.forEach((col, index) => {
                const checkbox = document.getElementById(`col_${index}`);
                if (checkbox && checkbox.checked) {
                    const method = document.getElementById(`method_${index}`).value;
                    const config = document.getElementById(`config_${index}`).value;
                    
                    if (method && config) {
                        tasks.push({
                            column: col,
                            method: method,
                            config: config
                        });
                    }
                }
            });
            
            return tasks;
        }

        function showTestResults(results) {
            const resultDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            
            let html = '<h4>试运行结果:</h4><table>';
            html += '<tr><th>列名</th><th>处理方式</th><th>配置</th><th>模拟结果</th></tr>';
            
            results.forEach(result => {
                html += `<tr>
                    <td>${result.column}</td>
                    <td>${result.method}</td>
                    <td>${result.config}</td>
                    <td>${result.result}</td>
                </tr>`;
            });
            
            html += '</table>';
            html += '<p style="color: #666;">注意：这是演示模式的模拟结果</p>';
            
            resultContent.innerHTML = html;
            resultDiv.classList.remove('hidden');
        }

        function showProcessResults() {
            const resultDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = `
                <h4>处理完成!</h4>
                <p>在完整版本中，处理结果将保存为新的Excel文件供下载。</p>
                <p style="color: #666;">当前为演示模式，实际功能需要完整的服务器环境。</p>
                <div style="margin-top: 20px; padding: 15px; background: #f0f8ff; border-radius: 5px;">
                    <h5>完整版本功能:</h5>
                    <ul>
                        <li>✅ 真实Excel文件读取和解析</li>
                        <li>✅ 实际的数据处理算法</li>
                        <li>✅ AI API集成</li>
                        <li>✅ 处理结果文件下载</li>
                        <li>✅ 错误处理和进度显示</li>
                    </ul>
                </div>
            `;
            resultDiv.classList.remove('hidden');
        }

        function setStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = type;
        }
    </script>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def handle_upload(self, post_data):
        """处理文件上传"""
        # 简化的响应
        response = {
            'success': True,
            'message': '演示模式 - 文件上传功能需要完整的服务器环境'
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def handle_process(self, post_data):
        """处理数据处理请求"""
        # 简化的响应
        response = {
            'success': True,
            'message': '演示模式 - 数据处理功能需要完整的服务器环境'
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode('utf-8'))

def main():
    """启动Web服务器"""
    PORT = 8000
    
    print("=" * 60)
    print("Excel数据处理工具 - Web演示版")
    print("=" * 60)
    print(f"启动Web服务器，端口: {PORT}")
    print(f"请在浏览器中访问: http://localhost:{PORT}")
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        with socketserver.TCPServer(("", PORT), ExcelHandler) as httpd:
            print(f"服务器已启动，监听端口 {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
