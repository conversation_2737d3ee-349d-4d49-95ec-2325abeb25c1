<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>灵犀技能类型对比</title>
  <style>
    body {
      font-family: "微软雅黑", Arial, sans-serif;
      background: #f7f9fa;
      color: #333;
      padding: 20px;
    }
    .container {
      max-width: 1000px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 30px;
    }
    h1 {
      text-align: center;
      color: #2a5d9f;
      margin-bottom: 30px;
    }
    .comparison-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 30px;
    }
    .comparison-table th, .comparison-table td {
      border: 1px solid #ddd;
      padding: 12px 15px;
      text-align: left;
      vertical-align: top;
    }
    .comparison-table th {
      background-color: #f0f4f8;
      color: #2a5d9f;
      font-weight: bold;
    }
    .comparison-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .comparison-table tr:hover {
      background-color: #f0f4f8;
    }
    .feature-name {
      font-weight: bold;
    }
    .skill-name {
      font-weight: bold;
      color: #2a5d9f;
    }
    .feature-list {
      list-style-type: none;
      padding-left: 0;
      margin: 0;
    }
    .feature-list li {
      margin-bottom: 8px;
      position: relative;
      padding-left: 20px;
    }
    .feature-list li:before {
      content: "•";
      position: absolute;
      left: 0;
      color: #2a5d9f;
    }
    .principle {
      background-color: #e8f4ff;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }
    .principle-title {
      font-weight: bold;
      color: #2a5d9f;
      margin-bottom: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>灵犀技能类型对比</h1>

    <table class="comparison-table">
      <thead>
        <tr>
          <th>特性</th>
          <th>快速问答</th>
          <th>API类型</th>
          <th>表单类型</th>
          <th>智能创建</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td class="feature-name">介绍</td>
          <td>用于灵犀机器人首页快速问答界面，一个机器人只有一个快速问答生效，原生只支持单msg文本入参</td>
          <td>提供给第三方使用（包括灵犀内部技能或外部系统调用），入参多样化</td>
          <td>可用于快速问答界面，也可提供给第三方使用</td>
          <td>灵犀试验性产品，通过自然语音创建API技能（非流程画布），支持通过代码编辑流程</td>
        </tr>
        <tr>
          <td class="feature-name">特点</td>
          <td>简单直接，易于使用，适合快速部署简单问答场景</td>
          <td>入参多样化，可一键生成API文档</td>
          <td>兼具交互性和服务性，不能一键生成API文档</td>
          <td>创建便捷，通过自然语言即可快速构建技能</td>
        </tr>
        <tr>
          <td class="feature-name">适用场景</td>
          <td>用于快速问答时选择</td>
          <td>对外提供服务，无交互使用场景时选择</td>
          <td>对外提供服务，有交互使用场景时选择</td>
          <td>试验性场景</td>
        </tr>
      </tbody>
    </table>

    <div style="background-color: #f9f9f9; padding: 15px; border-radius: 6px; margin-top: 20px;">
      <h3 style="color: #2a5d9f; margin-top: 0;">技能类型选择总结</h3>
      <p>灵犀平台提供四种技能类型，各有特点：</p>
      <ul style="margin-bottom: 0;">
        <li><strong>快速问答</strong>：单机器人一个，单文本入参，适合简单问答场景</li>
        <li><strong>API类型</strong>：多样入参，可生成API文档，适合系统集成</li>
        <li><strong>表单类型</strong>：可对外但不能生成API文档，适合交互场景</li>
        <li><strong>智能创建</strong>：试验性，自然语音创建API技能，降低技术门槛</li>
      </ul>
    </div>
  </div>
</body>
</html>
