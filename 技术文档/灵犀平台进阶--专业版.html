<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>灵犀平台进阶--深度使用技巧与经典案例拆解</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        :root {
            --primary-color: #00c389;
            --primary-light: rgba(0, 195, 137, 0.1);
            --primary-dark: #00a070;
            --secondary-color: #4a90e2;
            --text-color: #555;
            --heading-color: #333;
            --light-gray: #f5f7fa;
            --tip-color: #ff9800;
            --tip-bg: #fff8e1;
        }

        /* 添加动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Noto Sans SC', "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-color);
            background-color: #f0f2f5;
            line-height: 1.6;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
        }

        html {
            scroll-behavior: smooth;
            scroll-snap-type: y proximity;
            height: 100%;
        }

        /* 添加导航样式 */
        .nav-container {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .nav-container a {
            display: block;
            width: 12px;
            height: 12px;
            background-color: #ddd;
            border-radius: 50%;
            margin: 8px 0;
            transition: all 0.3s ease;
            position: relative;
            border: 2px solid transparent;
        }

        .nav-container a:hover,
        .nav-container a.active {
            background-color: var(--primary-color);
            transform: scale(1.2);
        }

        .nav-container a::after {
            content: attr(data-title);
            position: absolute;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
            white-space: nowrap;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .nav-container a:hover::after {
            opacity: 1;
        }



        .slide {
            width: 100%;
            max-width: 100%;
            height: 100vh;
            margin: 0;
            padding: 0;
            position: relative;
            overflow: hidden;
            box-shadow: none;
            background-color: #fff;
            animation: fadeIn 0.6s ease-out;
            animation-fill-mode: both;
            border-radius: 0;
            scroll-snap-align: start;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .slide:nth-child(even) {
            animation-delay: 0.1s;
        }

        .slide:nth-child(odd) {
            animation-delay: 0.2s;
        }

        .slide-content {
            padding: 40px 60px;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            box-sizing: border-box;
            position: relative;
            z-index: 2;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .slide-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .slide-number {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 14px;
            color: var(--primary-color);
            font-weight: 500;
            z-index: 3;
        }

        .slide-header {
            position: absolute;
            top: 0;
            left: 0;
            width: 12px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-color) 80%, transparent);
            z-index: 3;
            animation: slideInLeft 0.8s ease-out;
        }

        .slide-decoration {
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background-image: radial-gradient(circle at top right, var(--primary-light), transparent 70%);
            opacity: 0.7;
            z-index: 1;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 Z" fill="%2300c389" fill-opacity="0.1"/></svg>'),
                              radial-gradient(circle at top right, var(--primary-light), transparent 70%);
            background-size: 300px 300px, 300px 300px;
            background-repeat: no-repeat;
        }

        .slide-decoration-bottom {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 300px;
            height: 300px;
            opacity: 0.5;
            z-index: 1;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="80" cy="80" r="50" fill="%2300c389" fill-opacity="0.1"/></svg>'),
                              url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="5" fill="%2300c389" fill-opacity="0.05"/></svg>'),
                              radial-gradient(circle at bottom right, var(--primary-light), transparent 70%);
            background-position: bottom right, 30px 30px, center;
            background-size: 300px 300px, 100px 100px, cover;
            background-repeat: no-repeat, repeat, no-repeat;
        }

        h1 {
            font-size: 36px;
            color: #000;
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
            padding-left: 20px;
            background: linear-gradient(90deg, rgba(0, 195, 137, 0.1), transparent);
            padding: 10px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 195, 137, 0.1);
            display: inline-block;
        }

        h1:before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 36px;
            background-color: var(--primary-color);
            border-radius: 4px;
        }

        h2 {
            font-size: 32px;
            color: #000;
            margin-bottom: 25px;
            font-weight: 700;
            position: relative;
            border-bottom: 2px solid rgba(0, 195, 137, 0.3);
            padding-bottom: 10px;
            display: inline-block;
        }

        h2:after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 60px;
            height: 2px;
            background-color: var(--primary-color);
        }

        h3 {
            font-size: 28px;
            color: #000;
            margin-bottom: 20px;
            font-weight: 500;
            position: relative;
        }

        p, li {
            font-size: 22px;
            line-height: 1.6;
            color: #000;
        }

        ul, ol {
            margin-left: 10px;
            padding-left: 25px;
        }

        .image-placeholder {
            width: 85%;
            height: 150px;
            margin: 15px auto;
            border: 2px dashed var(--primary-color);
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--light-gray);
            color: var(--primary-color);
            font-size: 18px;
            border-radius: 8px;
            transition: all 0.3s ease;
            animation: pulse 2s infinite ease-in-out;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="%2300c389" fill-opacity="0.1"/></svg>'),
                              linear-gradient(45deg, var(--light-gray) 25%, rgba(255, 255, 255, 0.5) 25%, rgba(255, 255, 255, 0.5) 50%, var(--light-gray) 50%, var(--light-gray) 75%, rgba(255, 255, 255, 0.5) 75%, rgba(255, 255, 255, 0.5));
            background-position: center, 0 0;
            background-size: 64px 64px, 20px 20px;
            background-repeat: no-repeat, repeat;
            box-shadow: inset 0 0 15px rgba(0, 195, 137, 0.1);
        }

        .image-placeholder:hover {
            background-color: var(--primary-light);
            transform: scale(1.01);
            animation-play-state: paused;
        }

        .tip-box {
            background-color: var(--tip-bg);
            border-left: 4px solid var(--tip-color);
            padding: 12px 15px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.15);
            animation: fadeIn 0.5s ease-out;
            animation-delay: 0.3s;
            animation-fill-mode: both;
            position: relative;
            overflow: hidden;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><path fill="%23ffbb33" fill-opacity="0.05" d="M10 0L0 20h20L10 0z"/></svg>'),
                              url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="5" fill="%23ffbb33" fill-opacity="0.05"/></svg>');
            background-position: 10px 10px, 50px 50px;
            background-size: 100px 100px, 80px 80px;
        }

        .tip-box:after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255, 152, 0, 0.1) 0%, transparent 70%);
            border-radius: 50%;
        }

        .tip-title {
            font-weight: 700;
            color: var(--tip-color);
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            font-size: 18px;
        }

        .tip-title:before {
            content: "💡";
            margin-right: 10px;
            font-size: 20px;
        }

        .tip-content {
            color: #996600;
            font-size: 16px;
        }

        .tip-content ul {
            margin-top: 5px;
            margin-bottom: 5px;
        }

        .tip-content li {
            font-size: 16px;
        }

        .cover {
            background: linear-gradient(135deg, #fff, #f5f7fa);
            color: var(--heading-color);
            position: relative;
            overflow: hidden;
            animation: none;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="1000" height="1000" viewBox="0 0 1000 1000"><path d="M0,0 L1000,0 L1000,1000 L0,1000 Z" stroke="%2300c389" stroke-width="2" stroke-opacity="0.1" fill="none"/></svg>'),
                              url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" stroke="%2300c389" stroke-width="2" stroke-opacity="0.05" fill="none"/></svg>');
            background-position: center, 30px 30px;
            background-size: 80% 80%, 200px 200px;
            background-repeat: no-repeat, repeat;
        }

        .cover:before, .cover:after {
            animation: fadeIn 1.5s ease-out;
        }

        .cover:before {
            content: "";
            position: absolute;
            top: -100px;
            left: -100px;
            width: 300px;
            height: 300px;
            background-color: var(--primary-light);
            border-radius: 50%;
            z-index: 1;
        }

        .cover:after {
            content: "";
            position: absolute;
            bottom: -100px;
            right: -100px;
            width: 300px;
            height: 300px;
            background-color: var(--primary-light);
            border-radius: 50%;
            z-index: 1;
        }

        .cover .slide-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding-left: 100px;
            z-index: 2;
            animation: fadeIn 1s ease-out;
        }

        .cover h1 {
            color: #000;
            font-size: 48px;
            margin-bottom: 20px;
            padding-left: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            animation: fadeIn 1.2s ease-out;
            background: none;
            box-shadow: none;
            position: relative;
            display: inline-block;
        }

        .cover h1:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), transparent);
            border-radius: 2px;
        }

        .cover h1:before {
            display: none;
        }

        .cover h2 {
            color: var(--primary-color);
            font-size: 32px;
            font-weight: 400;
            margin-top: 0;
            animation: fadeIn 1.4s ease-out;
            border: none;
            position: relative;
            padding-left: 20px;
        }

        .cover h2:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background-color: var(--primary-color);
            border-radius: 50%;
        }

        .cover-decoration {
            position: absolute;
            top: 0;
            right: 0;
            width: 12px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-color) 80%, transparent);
            z-index: 3;
        }

        .end-slide {
            background: linear-gradient(135deg, #fff, #f5f7fa);
            color: var(--heading-color);
            position: relative;
            overflow: hidden;
        }

        .end-slide:before {
            content: "";
            position: absolute;
            top: -100px;
            left: -100px;
            width: 300px;
            height: 300px;
            background-color: var(--primary-light);
            border-radius: 50%;
            z-index: 1;
        }

        .end-slide:after {
            content: "";
            position: absolute;
            bottom: -100px;
            right: -100px;
            width: 300px;
            height: 300px;
            background-color: var(--primary-light);
            border-radius: 50%;
            z-index: 1;
        }

        .end-slide .slide-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            z-index: 2;
        }

        .end-slide h1 {
            color: var(--heading-color);
            font-size: 48px;
            margin-bottom: 20px;
            padding-left: 0;
        }

        .end-slide h1:before {
            display: none;
        }

        .end-slide h2 {
            color: var(--primary-color);
            font-size: 32px;
            font-weight: 400;
            margin-top: 0;
        }

        .nested-list {
            margin-left: 30px;
        }

        .nested-list li {
            font-size: 16px;
        }

        .toc-slide .slide-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .highlight {
            color: #000;
            font-weight: 500;
        }

        .content-title {
            font-size: 36px;
            color: #888;
            font-weight: 300;
            margin-bottom: 40px;
            text-transform: uppercase;
            letter-spacing: 2px;
            animation: fadeIn 0.8s ease-out;
            position: relative;
            display: inline-block;
            padding: 0 20px;
        }

        .content-title:before, .content-title:after {
            content: '';
            position: absolute;
            top: 50%;
            width: 30px;
            height: 2px;
            background-color: rgba(0, 195, 137, 0.3);
        }

        .content-title:before {
            left: -20px;
        }

        .content-title:after {
            right: -20px;
        }

        .toc-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            animation: fadeIn 0.5s ease-out;
            animation-fill-mode: both;
        }

        .toc-item:nth-child(1) { animation-delay: 0.6s; }
        .toc-item:nth-child(2) { animation-delay: 0.8s; }
        .toc-item:nth-child(3) { animation-delay: 1.0s; }
        .toc-item:nth-child(4) { animation-delay: 1.2s; }
        .toc-item:nth-child(5) { animation-delay: 1.4s; }

        .toc-number {
            font-size: 28px;
            color: #000;
            font-weight: 700;
            margin-right: 20px;
            width: 40px;
            text-align: right;
            position: relative;
            z-index: 2;
        }

        .toc-number:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background-color: rgba(0, 195, 137, 0.1);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .toc-item:hover .toc-number:after {
            opacity: 1;
        }

        .toc-text {
            font-size: 28px;
            color: #000;
            font-weight: 500;
        }

        .toc-line {
            width: 4px;
            height: 300px;
            background: linear-gradient(to bottom, var(--primary-color), transparent);
            position: absolute;
            left: 120px;
            top: 150px;
            animation: slideInLeft 1s ease-out;
            animation-delay: 0.5s;
            animation-fill-mode: both;
            box-shadow: 0 0 10px rgba(0, 195, 137, 0.3);
            border-radius: 4px;
        }

        .toc-line:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 30px;
            background-color: var(--primary-color);
            border-radius: 4px;
            animation: pulse 2s infinite;
        }

        .section-title {
            font-size: 24px;
            color: #000;
            font-weight: 500;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            background: linear-gradient(90deg, rgba(0, 195, 137, 0.05) 0%, transparent 100%);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            width: fit-content;
        }

        .section-title:before {
            content: "";
            width: 8px;
            height: 8px;
            background-color: var(--primary-color);
            border-radius: 50%;
            margin-right: 10px;
            display: inline-block;
            animation: pulse 2s infinite;
        }

        @media print {
            body {
                background-color: white;
            }

            .slide {
                margin: 0;
                page-break-after: always;
                box-shadow: none;
                border: none;
                animation: none !important;
            }

            .image-placeholder,
            .section-title:before,
            .tip-box {
                animation: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="nav-container">
        <a href="#cover" data-title="封面"></a>
        <a href="#share-benefits" data-title="分享收益"></a>
        <a href="#toc" data-title="目录"></a>
        <a href="#knowledge" data-title="知识库"></a>
        <a href="#skill-management" data-title="技能管理"></a>
        <a href="#skill-orchestration-parallel" data-title="节点并行"></a>
        <a href="#skill-orchestration-async" data-title="异步调用"></a>
        <a href="#skill-orchestration-same-node" data-title="节点同名"></a>
        <a href="#session-memory" data-title="会话记忆"></a>
        <a href="#batch-test" data-title="批量测试"></a>
        <a href="#other-modules" data-title="其他模块"></a>
        <a href="#system-config" data-title="系统配置"></a>
        <a href="#case-study" data-title="经典案例拆解"></a>
        <a href="#thank-you" data-title="感谢观看"></a>
    </div>
    <!-- 幻灯片1：封面 -->
    <div class="slide cover" id="cover">
        <div class="cover-decoration"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <div class="slide-content">
            <h1>灵犀平台进阶</h1>
            <h2>深度使用技巧与经典案例拆解</h2>
        </div>
    </div>

    <!-- 幻灯片2.5：分享收益 -->
    <div class="slide" id="share-benefits">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <div class="slide-content">
        <h2>通过分享将Get的技能</h2>

        <div style="display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 30px;">
            <div style="width: 48%; margin-bottom: 30px; background: linear-gradient(135deg, rgba(0,195,137,0.05) 0%, transparent 100%); padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                <div class="section-title">在灵犀平台玩转知识库RAG</div>
                <p style="font-size: 18px;">掌握三类知识库的使用场景与最佳实践，提升模型回答的准确性与相关性</p>
            </div>

            <div style="width: 48%; margin-bottom: 30px; background: linear-gradient(135deg, rgba(0,195,137,0.05) 0%, transparent 100%); padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                <div class="section-title">成为AIGC技能编排高手</div>
                <p style="font-size: 18px;">学习并行节点、异步调用、会话记忆等技巧，构建复杂而高效的AI流程</p>
            </div>

            <div style="width: 48%; background: linear-gradient(135deg, rgba(0,195,137,0.05) 0%, transparent 100%); padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                <div class="section-title">从现在开始沉淀测试集</div>
                <p style="font-size: 18px;">用批量测试准确率代替主观感受，实现模型效果的客观评估与持续追踪</p>
            </div>

            <div style="width: 48%; background: linear-gradient(135deg, rgba(0,195,137,0.05) 0%, transparent 100%); padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                <div class="section-title">了解我们踩过的坑</div>
                <p style="font-size: 18px;">了解各个模块的"避坑指南"，减少重复犯错，提升开发效率</p>
            </div>
        </div>
    </div>
    </div>

    <!-- 幻灯片2：目录 -->
    <div class="slide toc-slide" id="toc">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <div class="slide-content">
            <div class="content-title">CONTENT</div>
            <div class="toc-line"></div>

            <div class="toc-item">
                <div class="toc-number">I.</div>
                <div class="toc-text">知识库</div>
            </div>
            <div class="toc-item">
                <div class="toc-number">II.</div>
                <div class="toc-text">技能管理</div>
            </div>
            <div class="toc-item">
                <div class="toc-number">III.</div>
                <div class="toc-text">其他模块</div>
            </div>
            <div class="toc-item">
                <div class="toc-number">IV.</div>
                <div class="toc-text">经典案例拆解</div>
            </div>
        </div>

    </div>

    <!-- 幻灯片3：灵犀主要模块详解 - 知识库 -->
    <div class="slide" id="knowledge">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <div class="slide-content">
            <h2>知识库</h2>

            <img src="三类知识库介绍与对比.png" alt="三类知识库介绍与对比" style="width: 85%; height: auto; margin: 15px auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
            <div class="tip-box">
                <div class="tip-title">避坑指南</div>
                <div class="tip-content">文档可以通过外部大模型工具（如cursor、deepseek r1等）处理为FAQ集，再导入问答知识库，效果会更好</div>
            </div>
        </div>
    </div>

    <!-- 幻灯片4：灵犀主要模块详解 - 技能管理 -->
    <div class="slide" id="skill-management">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <div class="slide-content">
            <h2>技能管理</h2>

            <img src="技能类型区别与对比.png" alt="技能类型区别与对比" style="width: 85%; height: auto; margin: 15px auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
            <div class="tip-box">
                <div class="tip-title">避坑指南</div>
                <div class="tip-content">
                    <ul>
                        <li>测试与生产保持技能编码一致，可以极大减少发布工作量</li>
                        <li>不同的技能类型不能互相导入，如API技能的导出文件，不能导入到表单技能</li>
                    </ul>
                </div>
            </div>
        </div>

    </div>

    <!-- 幻灯片5：技能编排 - 节点并行 -->
    <div class="slide" id="skill-orchestration-parallel">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <!-- 添加SVG装饰元素 -->
        <div style="position: absolute; top: 15%; left: 8%; z-index: 1; opacity: 0.1;">
            <svg width="140" height="140" viewBox="0 0 140 140" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <path d="M20,70 L120,70" stroke-dasharray="5,5"></path>
                    <path d="M20,40 L120,40" stroke-dasharray="5,5"></path>
                    <path d="M20,100 L120,100" stroke-dasharray="5,5"></path>
                    <path d="M20,40 L20,100" stroke-dasharray="5,5"></path>
                    <path d="M120,40 L120,100" stroke-dasharray="5,5"></path>
                    <circle cx="20" cy="40" r="5"></circle>
                    <circle cx="20" cy="70" r="5"></circle>
                    <circle cx="20" cy="100" r="5"></circle>
                    <circle cx="120" cy="40" r="5"></circle>
                    <circle cx="120" cy="70" r="5"></circle>
                    <circle cx="120" cy="100" r="5"></circle>
                </g>
            </svg>
        </div>
        <div class="slide-content">
            <h2>技能编排</h2>
            <div class="section-title">节点并行缩短耗时</div>
            <p>三条并行线路的耗时为最长的一段耗时，可显著提升整体响应速度。无依赖关系的节点，推荐使用并行。</p>
            <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
            <div style="width: 80%; margin: 20px auto;">
                <img src="节点并行缩短耗时.png" alt="节点并行缩短耗时" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
            </div>
        </div>
    </div>

    <!-- 幻灯片5.5：技能编排 - 异步调用 -->
    <div class="slide" id="skill-orchestration-async">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <!-- 添加SVG装饰元素 -->
        <div style="position: absolute; bottom: 20%; right: 10%; z-index: 1; opacity: 0.1;">
            <svg width="160" height="160" viewBox="0 0 160 160" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <path d="M30,80 C30,50 130,50 130,80 C130,110 30,110 30,80 Z"></path>
                    <path d="M30,80 L70,80" stroke-dasharray="5,5"></path>
                    <path d="M90,80 L130,80" stroke-dasharray="5,5"></path>
                    <circle cx="80" cy="80" r="10"></circle>
                    <path d="M80,50 L80,110" stroke-dasharray="5,5"></path>
                </g>
            </svg>
        </div>
        <div class="slide-content">
            <h2>技能编排</h2>
            <div class="section-title">异步调用</div>
            <p>不阻塞流程，流程也拿不到返回值，适用于只需触发不需要等待结果的场景，如触达、埋点、告警等</p>
            <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
            <div style="width: 80%; margin: 20px auto;">
                <img src="异步调用不阻塞流程.png" alt="异步调用不阻塞流程" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
            </div>
        </div>
    </div>

    <!-- 幻灯片6：技能编排 - 节点同名 -->
    <div class="slide" id="skill-orchestration-same-node">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <!-- 添加SVG装饰元素 -->
        <div style="position: absolute; top: 15%; right: 10%; z-index: 1; opacity: 0.1;">
            <svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <circle cx="75" cy="40" r="15"></circle>
                    <circle cx="40" cy="110" r="15"></circle>
                    <circle cx="110" cy="110" r="15"></circle>
                    <path d="M75,55 L40,95" stroke-dasharray="5,5"></path>
                    <path d="M75,55 L110,95" stroke-dasharray="5,5"></path>
                </g>
            </svg>
        </div>
        <div style="position: absolute; bottom: 15%; left: 5%; z-index: 1; opacity: 0.1;">
            <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <rect x="20" y="20" width="80" height="80" rx="10"></rect>
                    <path d="M20,60 L100,60" stroke-dasharray="5,5"></path>
                    <path d="M60,20 L60,100" stroke-dasharray="5,5"></path>
                    <circle cx="60" cy="60" r="15"></circle>
                </g>
            </svg>
        </div>
        <div class="slide-content">
            <h2>技能编排</h2>
            <div class="section-title">节点同名</div>
            <p>多个节点同名时，后续节点的取值，优先取任一有值的节点 <span style="color:#ff6b6b; font-weight: bold;">* 注意：多个节点有值也只会取其一</span></p>

            <div style="display: flex; justify-content: space-between; margin-top: 20px;">
                <div style="width: 48%; position: relative;">
                    <p class="section-title" style="font-size: 18px;">a. 利用条件节点控制只执行一个节点</p>
                    <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
                    <div style="width: 100%; margin: 10px auto;">
                        <img src="利用条件节点控制只执行一个节点.png" alt="利用条件节点控制只执行一个节点" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
                    </div>
                </div>
                <div style="width: 48%; position: relative;">
                    <p class="section-title" style="font-size: 18px;">b. 利用前置判断控制只执行一个节点</p>
                    <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
                    <div style="width: 100%; margin: 10px auto;">
                        <img src="利用前置判断控制只执行一个节点.png" alt="利用前置判断控制只执行一个节点" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片9：会话记忆 -->
    <div class="slide" id="session-memory">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <!-- 添加SVG装饰元素 -->
        <div style="position: absolute; top: 20%; left: 5%; z-index: 1; opacity: 0.1;">
            <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <circle cx="60" cy="60" r="50"></circle>
                    <circle cx="60" cy="60" r="40"></circle>
                    <circle cx="60" cy="60" r="30"></circle>
                    <path d="M60,10 L60,110 M10,60 L110,60" stroke-dasharray="5,5"></path>
                </g>
            </svg>
        </div>
        <div style="position: absolute; bottom: 15%; right: 5%; z-index: 1; opacity: 0.1;">
            <svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <rect x="25" y="25" width="100" height="100" rx="10"></rect>
                    <rect x="45" y="45" width="60" height="60" rx="5"></rect>
                    <path d="M25,75 L125,75 M75,25 L75,125" stroke-dasharray="5,5"></path>
                </g>
            </svg>
        </div>
        <div class="slide-content">
            <h2>会话记忆</h2>
            <ol>
                <li>生命周期永久，可理解为key为sessionId的持久化对象</li>
                <li>输入节点需新增入参字段：sessionId，空值时会随机一个值</li>
                <li>设置与使用：</li>
            </ol>

            <div style="display: flex; justify-content: space-between; margin-top: 20px; position: relative;">
                <div style="width: 48%; position: relative;">
                    <p class="section-title" style="font-size: 18px;">a. 利用后置处理设置值</p>
                    <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
                    <div style="width: 100%; margin: 10px auto;">
                        <img src="利用后置处理设置值.png" alt="利用后置处理设置值" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
                    </div>
                </div>
                <div style="width: 48%; position: relative;">
                    <p class="section-title" style="font-size: 18px;">b. 与流程变量、全局变量同样的使用规则</p>
                    <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
                    <div style="width: 100%; margin: 10px auto;">
                        <img src="与流程变量、全局变量同样的使用规则.png" alt="与流程变量、全局变量同样的使用规则" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
                    </div>
                </div>
            </div>
            <div class="tip-box">
                <div class="tip-title">避坑指南</div>
                <div class="tip-content">
                    <ul>
                        <li>会话记忆是key为sessionId的对象，所以要避免同时在不同的节点对同一session进行写操作，可能导致写丢失</li>
                        <li>有条件的系统，建议由后端提供统一的redis读写接口，会安全一些</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片11：批量测试(1) -->
    <div class="slide" id="batch-test">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <!-- 添加SVG装饰元素 -->
        <div style="position: absolute; top: 20%; right: 8%; z-index: 1; opacity: 0.1;">
            <svg width="180" height="180" viewBox="0 0 180 180" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <rect x="30" y="30" width="120" height="30" rx="5"></rect>
                    <rect x="30" y="75" width="120" height="30" rx="5"></rect>
                    <rect x="30" y="120" width="120" height="30" rx="5"></rect>
                    <path d="M45,30 L45,150" stroke-dasharray="5,5"></path>
                    <path d="M75,30 L75,150" stroke-dasharray="5,5"></path>
                    <path d="M105,30 L105,150" stroke-dasharray="5,5"></path>
                    <path d="M135,30 L135,150" stroke-dasharray="5,5"></path>
                    <circle cx="45" cy="45" r="5"></circle>
                    <circle cx="75" cy="45" r="5"></circle>
                    <circle cx="105" cy="90" r="5"></circle>
                    <circle cx="135" cy="135" r="5"></circle>
                </g>
            </svg>
        </div>
        <div style="position: absolute; bottom: 10%; left: 5%; z-index: 1; opacity: 0.1;">
            <svg width="140" height="140" viewBox="0 0 140 140" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <path d="M20,20 L120,20 L120,120 L20,120 Z"></path>
                    <path d="M40,40 L100,40" stroke-dasharray="5,5"></path>
                    <path d="M40,60 L100,60" stroke-dasharray="5,5"></path>
                    <path d="M40,80 L100,80" stroke-dasharray="5,5"></path>
                    <path d="M40,100 L100,100" stroke-dasharray="5,5"></path>
                    <circle cx="30" cy="40" r="4"></circle>
                    <circle cx="30" cy="60" r="4"></circle>
                    <circle cx="30" cy="80" r="4"></circle>
                    <circle cx="30" cy="100" r="4"></circle>
                </g>
            </svg>
        </div>
        <div class="slide-content">
            <h2>批量测试</h2>
            <ol>
                <li>选择对比字段：用于对比测试结果的字段</li>
                <li>断言类型：指如何标注输出结果是否准确
                    <ol type="a">
                        <li>文本对比：字符串==和equal的功能，完全一致视为输出结果正确</li>
                        <li>模型断言：使用自定义提示词对比测试输出与期望结果</li>
                    </ol>
                </li>
                <li>导入方式区别
                    <ol type="a">
                        <li>测试集导入：机器人维度，可提前导入的测试集</li>
                        <li>模板导入：单技能维度的测试集（推荐使用）</li>
                    </ol>
                </li>
            </ol>
            <div class="tip-box" style="position: relative; overflow: hidden;">
                <div class="tip-title">避坑指南</div>
                <div class="tip-content">
                    <ul>
                        <li>表单类型的技能数字类型输入字段在批量测试时可能会有类型不匹配问题
                            <ul class="nested-list">
                                <li>使用API类型技能</li>
                                <li>数字型字段改为字符串</li>
                                <li>输入节点后使用Script节点处理字段类型</li>
                            </ul>
                        </li>
                    </ul>
                </div>
                <!-- 添加小图标装饰 -->
                <div style="position: absolute; bottom: 10px; right: 10px; opacity: 0.2;">
                    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20,5 L35,30 L5,30 Z" fill="#ff9800"></path>
                        <text x="20" y="25" text-anchor="middle" fill="#fff" font-size="15">!</text>
                    </svg>
                </div>
            </div>
        </div>
    </div>
<!-- 幻灯片13：其他模块 - 数据管理 -->
    <div class="slide" id="other-modules">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <!-- 添加SVG装饰元素 -->
        <div style="position: absolute; top: 15%; left: 10%; z-index: 1; opacity: 0.1;">
            <svg width="160" height="160" viewBox="0 0 160 160" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <path d="M30,40 L130,40" stroke-dasharray="5,5"></path>
                    <path d="M30,70 L130,70" stroke-dasharray="5,5"></path>
                    <path d="M30,100 L130,100" stroke-dasharray="5,5"></path>
                    <path d="M30,130 L130,130" stroke-dasharray="5,5"></path>
                    <rect x="30" y="40" width="100" height="90" rx="5"></rect>
                    <circle cx="50" cy="55" r="5"></circle>
                    <circle cx="50" cy="85" r="5"></circle>
                    <circle cx="50" cy="115" r="5"></circle>
                    <path d="M70,55 L110,55" stroke-width="4" stroke-linecap="round"></path>
                    <path d="M70,85 L110,85" stroke-width="4" stroke-linecap="round"></path>
                    <path d="M70,115 L110,115" stroke-width="4" stroke-linecap="round"></path>
                </g>
            </svg>
        </div>
        <div class="slide-content">
            <h2>数据管理</h2>
            <div class="section-title">调用日志：请求日志查询</div>
            <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
            <div style="width: 85%; margin: 15px auto;">
                <img src="调用日志.png" alt="调用日志" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
            </div>
            <div class="tip-box" style="position: relative; overflow: hidden;">
                <div class="tip-title">避坑指南</div>
                <div class="tip-content">通过调用技能节点调用的技能，在调用日志中不能被查询到，只能通过入口技能查看</div>
                <!-- 添加小图标装饰 -->
                <div style="position: absolute; bottom: 10px; right: 10px; opacity: 0.2;">
                    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20,5 L35,30 L5,30 Z" fill="#ff9800"></path>
                        <text x="20" y="25" text-anchor="middle" fill="#fff" font-size="15">!</text>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片14：系统配置 -->
    <div class="slide" id="system-config">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <!-- 添加SVG装饰元素 -->
        <div style="position: absolute; bottom: 20%; right: 10%; z-index: 1; opacity: 0.1;">
            <svg width="170" height="170" viewBox="0 0 170 170" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <circle cx="85" cy="85" r="60"></circle>
                    <circle cx="85" cy="85" r="40"></circle>
                    <path d="M85,25 L85,145" stroke-dasharray="5,5"></path>
                    <path d="M25,85 L145,85" stroke-dasharray="5,5"></path>
                    <path d="M45,45 L125,125" stroke-dasharray="5,5"></path>
                    <path d="M45,125 L125,45" stroke-dasharray="5,5"></path>
                    <circle cx="85" cy="85" r="15"></circle>
                    <circle cx="85" cy="85" r="5"></circle>
                </g>
            </svg>
        </div>
        <div class="slide-content">
            <h2>系统配置</h2>
            <div class="section-title">API访问：用于外部系统调用</div>
            <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
            <div style="width: 85%; margin: 15px auto;">
                <img src="API访问.png" alt="API访问" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
            </div>
        </div>
    </div>

    <!-- 幻灯片15：经典案例拆解 -->
    <div class="slide" id="case-study">
        <div class="slide-header"></div>
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <!-- 添加SVG装饰元素 -->
        <div style="position: absolute; top: 20%; left: 8%; z-index: 1; opacity: 0.1;">
            <svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <path d="M30,30 L120,30 L120,120 L30,120 Z"></path>
                    <path d="M45,45 L105,45" stroke-width="3" stroke-linecap="round"></path>
                    <path d="M45,60 L105,60" stroke-width="2" stroke-linecap="round"></path>
                    <path d="M45,75 L105,75" stroke-width="2" stroke-linecap="round"></path>
                    <path d="M45,90 L85,90" stroke-width="2" stroke-linecap="round"></path>
                    <path d="M45,105 L75,105" stroke-width="2" stroke-linecap="round"></path>
                </g>
            </svg>
        </div>
        <div style="position: absolute; bottom: 15%; right: 8%; z-index: 1; opacity: 0.1;">
            <svg width="140" height="140" viewBox="0 0 140 140" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" stroke="#00c389" stroke-width="2">
                    <path d="M20,40 C20,30 120,30 120,40 C120,50 20,50 20,40 Z"></path>
                    <path d="M20,70 C20,60 120,60 120,70 C120,80 20,80 20,70 Z"></path>
                    <path d="M20,100 C20,90 120,90 120,100 C120,110 20,110 20,100 Z"></path>
                    <circle cx="70" cy="40" r="5"></circle>
                    <circle cx="70" cy="70" r="5"></circle>
                    <circle cx="70" cy="100" r="5"></circle>
                </g>
            </svg>
        </div>
        <div class="slide-content">
            <h2>经典案例拆解</h2>
            <div class="section-title">文案检查助手</div>
            <p>一个帮助用户检查文案规范性的实用工具，可快速发现文案中的问题并给出修改建议</p>
            <!-- 简化图片容器，使其在PDF导出时能正确显示 -->
            <div style="width: 85%; margin: 15px auto;">
                <img src="文案检查助手的输出案例.png" alt="文案检查助手的输出案例" style="width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
            </div>
        </div>
    </div>

    <!-- 幻灯片16：结束页 -->
    <div class="slide end-slide" id="thank-you">
        <div class="slide-decoration"></div>
        <div class="slide-decoration-bottom"></div>
        <div class="slide-content">
            <h1>谢谢</h1>
            <h2>感谢观看</h2>
        </div>
    </div>
    <script>
        // 等待页面加载完成
        window.onload = function() {
            // 获取所有导航链接
            const navLinks = document.querySelectorAll('.nav-container a');
            // 获取所有幻灯片
            const slides = document.querySelectorAll('.slide');

            // 初始化页面滚动位置
            setTimeout(function() {
                window.scrollTo(0, 0);

                // 默认选中第一个导航点
                if (navLinks.length > 0) {
                    navLinks[0].classList.add('active');
                }
            }, 100);

            // 添加滚动事件监听器
            window.addEventListener('scroll', function() {
                // 获取当前滚动位置
                const scrollPosition = window.scrollY;

                // 检查每个幻灯片的位置
                slides.forEach(function(slide) {
                    const slideTop = slide.offsetTop;
                    const slideHeight = slide.offsetHeight;

                    // 如果当前滚动位置在幻灯片范围内
                    if (scrollPosition >= slideTop - 100 && scrollPosition < slideTop + slideHeight - 100) {
                        // 移除所有导航链接的活动状态
                        navLinks.forEach(function(link) {
                            link.classList.remove('active');
                        });

                        // 如果幻灯片有ID，则高亮对应的导航链接
                        if (slide.id) {
                            const activeLink = document.querySelector('.nav-container a[href="#' + slide.id + '"]');
                            if (activeLink) {
                                activeLink.classList.add('active');
                            }
                        }
                    }
                });
            });

            // 添加导航链接点击事件
            navLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        // 平滑滚动到目标元素
                        window.scrollTo({
                            top: targetElement.offsetTop,
                            behavior: 'smooth'
                        });

                        // 更新活动状态
                        navLinks.forEach(function(navLink) {
                            navLink.classList.remove('active');
                        });
                        link.classList.add('active');
                    }
                });
            });

            // 添加鼠标滚轮事件
            window.addEventListener('wheel', function(e) {
                e.preventDefault();

                // 获取当前可见的幻灯片
                const scrollPosition = window.scrollY;
                let currentIndex = 0;

                slides.forEach(function(slide, index) {
                    const slideTop = slide.offsetTop;
                    const slideHeight = slide.offsetHeight;

                    if (scrollPosition >= slideTop - slideHeight/2 &&
                        scrollPosition < slideTop + slideHeight/2) {
                        currentIndex = index;
                    }
                });

                // 根据滚轮方向决定滚动到上一页或下一页
                if (e.deltaY > 0 && currentIndex < slides.length - 1) {
                    // 向下滚动
                    window.scrollTo({
                        top: slides[currentIndex + 1].offsetTop,
                        behavior: 'smooth'
                    });
                } else if (e.deltaY < 0 && currentIndex > 0) {
                    // 向上滚动
                    window.scrollTo({
                        top: slides[currentIndex - 1].offsetTop,
                        behavior: 'smooth'
                    });
                }
            }, { passive: false });

            // 添加键盘事件监听器
            document.addEventListener('keydown', function(e) {
                // 当前可见的幻灯片索引
                let currentIndex = 0;
                let minDistance = Infinity;

                // 找到当前可见的幻灯片
                const scrollPosition = window.scrollY;
                slides.forEach(function(slide, index) {
                    const distance = Math.abs(slide.offsetTop - scrollPosition);
                    if (distance < minDistance) {
                        minDistance = distance;
                        currentIndex = index;
                    }
                });

                // 上下箭头或空格键切换幻灯片
                if (e.key === 'ArrowDown' || e.key === ' ' || e.key === 'PageDown') {
                    e.preventDefault();
                    if (currentIndex < slides.length - 1) {
                        window.scrollTo({
                            top: slides[currentIndex + 1].offsetTop,
                            behavior: 'smooth'
                        });
                    }
                } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
                    e.preventDefault();
                    if (currentIndex > 0) {
                        window.scrollTo({
                            top: slides[currentIndex - 1].offsetTop,
                            behavior: 'smooth'
                        });
                    }
                }
            });

            // 添加触摸滚动支持
            let touchStartY = 0;
            let touchEndY = 0;

            document.addEventListener('touchstart', function(e) {
                touchStartY = e.changedTouches[0].screenY;
            }, false);

            document.addEventListener('touchend', function(e) {
                touchEndY = e.changedTouches[0].screenY;
                handleSwipe();
            }, false);

            function handleSwipe() {
                // 获取当前可见的幻灯片
                const scrollPosition = window.scrollY;
                let currentIndex = 0;

                slides.forEach(function(slide, index) {
                    const slideTop = slide.offsetTop;
                    const slideHeight = slide.offsetHeight;

                    if (scrollPosition >= slideTop - slideHeight/2 &&
                        scrollPosition < slideTop + slideHeight/2) {
                        currentIndex = index;
                    }
                });

                if (touchEndY < touchStartY - 50 && currentIndex < slides.length - 1) {
                    // 向上滑动，显示下一页
                    window.scrollTo({
                        top: slides[currentIndex + 1].offsetTop,
                        behavior: 'smooth'
                    });
                } else if (touchEndY > touchStartY + 50 && currentIndex > 0) {
                    // 向下滑动，显示上一页
                    window.scrollTo({
                        top: slides[currentIndex - 1].offsetTop,
                        behavior: 'smooth'
                    });
                }
            }
        };
    </script>
</body>
</html>