@tool
def main(oper='', name='', content='', date='', msg=''):
    # 完成冒泡算法
    def bubble_sort(arr):
        n = len(arr)
        for i in range(n):
            for j in range(0, n-i-1):
                if arr[j] > arr[j+1]:
                    arr[j], arr[j+1] = arr[j+1], arr[j]
        return arr

    # 完成快速排序算法
    def quick_sort(arr):
        if len(arr) <= 1:
            return arr
        pivot = arr[len(arr) // 2]
        left = [x for x in arr if x < pivot]
        middle = [x for x in arr if x == pivot]
        right = [x for x in arr if x > pivot]
        return quick_sort(left) + middle + quick_sort(right)

    # 完成二分查找算法
    def binary_search(arr, target):
        low, high = 0, len(arr) - 1
        while low <= high:
            mid = (low + high) // 2
            if arr[mid] < target:
                low = mid + 1
            elif arr[mid] > target:
                high = mid - 1
            else:
                return mid
        return -1
    
    # 测试
    print("冒泡排序测试:", bubble_sort([3, 2, 1, 5, 4]))
    print("快速排序测试:", quick_sort([3, 2, 1, 5, 4]))
    print("二分查找测试:", binary_search([1, 2, 3, 4, 5], 3))
