import re

# 测试模板
templates = [
    "<#if test_var??>test_var存在</#if>",
    "<#if missing_var??>missing_var存在</#if>",
    "<#if 降级对比图片链接??>降级对比图片链接存在</#if>",
    '<#if 降级对比图片链接??>,{ "imageUrl": "${降级对比图片链接}", "type": "image" }</#if>'
]

# 提取if条件
for i, template in enumerate(templates):
    print(f"模板 {i+1}: {template}")
    
    # 提取条件
    condition_match = re.search(r"<#if\s+(.+?)>", template)
    if condition_match:
        condition = condition_match.group(1).strip()
        print(f"  提取的条件: '{condition}'")
        
        # 检查是否包含??
        if "??" in condition:
            var_name = condition.replace("??", "").strip()
            print(f"  变量名: '{var_name}'")
    else:
        print("  未找到条件")
    
    # 提取内容
    content_match = re.search(r"<#if\s+.+?>(.*?)</#if>", template, re.DOTALL)
    if content_match:
        content = content_match.group(1)
        print(f"  提取的内容: '{content}'")
    else:
        print("  未找到内容")
    
    print("-" * 30) 