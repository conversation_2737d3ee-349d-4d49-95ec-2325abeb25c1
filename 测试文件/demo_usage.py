#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel处理应用演示脚本
展示如何使用各种功能处理实际数据
"""

import pandas as pd
import json

def create_demo_data():
    """创建演示数据"""
    demo_data = {
        '数值列': [100, 200, 300, 400, 500],
        'JSON数据': [
            '{"name": "张三", "score": 85, "grade": "A"}',
            '{"name": "李四", "score": 92, "grade": "A+"}',
            '{"name": "王五", "score": 78, "grade": "B"}',
            '{"name": "赵六", "score": 88, "grade": "A"}',
            '{"name": "钱七", "score": 95, "grade": "A+"}',
        ],
        '时间数据': [
            '2024-06-15 09:00:00',
            '2024-06-15 10:30:00',
            '2024-06-15 14:20:00',
            '2024-06-15 16:45:00',
            '2024-06-15 18:10:00',
        ],
        '文本内容': [
            '用户反馈：产品功能很好，但界面需要优化',
            '客服记录：用户咨询价格信息，已提供报价',
            '系统日志：数据同步完成，耗时3.2秒',
            '用户评价：服务态度很好，解决问题及时',
            '错误报告：网络连接超时，请检查网络设置',
        ]
    }
    
    df = pd.DataFrame(demo_data)
    df.to_excel('演示数据.xlsx', index=False)
    print("演示数据已创建：演示数据.xlsx")
    return df

def show_usage_examples():
    """显示使用示例"""
    print("\n=== Excel数据处理工具使用示例 ===\n")
    
    print("1. 执行算式示例：")
    print("   列：数值列")
    print("   公式：x * 1.1  (给数值增加10%)")
    print("   结果：110, 220, 330, 440, 550")
    print()
    
    print("2. 按字段取值示例：")
    print("   列：JSON数据")
    print("   字段：name|score  (提取姓名和分数)")
    print("   结果：张三|85, 李四|92, 王五|78, ...")
    print()
    
    print("3. AI写公式示例：")
    print("   列：数值列")
    print("   描述：计算当前行数值的平方")
    print("   生成公式：=POWER(A2,2)")
    print()
    
    print("4. AI自定义任务示例：")
    print("   列：文本内容")
    print("   任务：分析文本情感倾向（正面/负面/中性）")
    print("   结果：正面, 中性, 中性, 正面, 负面")
    print()

def show_step_by_step_guide():
    """显示分步操作指南"""
    print("=== 分步操作指南 ===\n")
    
    steps = [
        "1. 启动应用：python3 excel_processor_app.py",
        "2. 点击'选择Excel文件'，选择要处理的文件",
        "3. 点击'读取Excel'，加载文件数据",
        "4. 选择结果存储方式（表格后方空白列 或 新建工作簿）",
        "5. 对需要处理的列进行配置：",
        "   - 勾选'是否处理'复选框",
        "   - 选择处理方式",
        "   - 填写任务详情",
        "6. 点击'试运行'验证配置（推荐）",
        "7. 确认无误后点击'开始执行'",
        "8. 等待处理完成，查看结果"
    ]
    
    for step in steps:
        print(step)
    print()

def show_tips_and_tricks():
    """显示使用技巧"""
    print("=== 使用技巧 ===\n")
    
    tips = [
        "💡 建议先用'试运行'功能测试配置是否正确",
        "💡 算式处理支持常见数学运算：+, -, *, /, **, ()",
        "💡 JSON字段提取支持嵌套对象，用点号分隔：data.user.name",
        "💡 AI功能需要网络连接，处理大量数据时请耐心等待",
        "💡 可以同时配置多列进行批量处理",
        "💡 处理过程中会显示进度条和状态信息",
        "💡 如果处理失败，结果会显示为空值",
        "💡 新建工作簿会在原文件同目录下创建"
    ]
    
    for tip in tips:
        print(tip)
    print()

def main():
    """主函数"""
    print("Excel数据处理工具演示")
    print("=" * 50)
    
    # 创建演示数据
    df = create_demo_data()
    print(f"演示数据预览：\n{df.head()}\n")
    
    # 显示使用示例
    show_usage_examples()
    
    # 显示操作指南
    show_step_by_step_guide()
    
    # 显示使用技巧
    show_tips_and_tricks()
    
    print("现在可以使用演示数据.xlsx文件来测试应用功能！")
    print("启动命令：python3 excel_processor_app.py")

if __name__ == "__main__":
    main()
