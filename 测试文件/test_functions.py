#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel处理应用的各个功能
"""

import pandas as pd
import json
import requests
import logging

class TestProcessor:
    def __init__(self):
        logging.basicConfig(level=logging.INFO)
    
    def callAPI(self, prompt, model="qwen-72b", session_id=None):
        """测试API调用"""
        url = "http://za-aigc-platform.test.za.biz/bots/lytestrobot/lycommonpromptskill/execute"
        headers = {
            'Content-Type': 'application/json',
            'access-key': '20240618170943ZWCOVXFBYROERESODN'
        }

        data = {
            "model": model,
            "prompt": prompt,
            "sessionId": session_id
        }

        try:
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            if 'data' in result:
                return result['data']
            else:
                logging.warning(f"API response does not contain 'data' field. Full response: {result}")
                return ""
        except requests.RequestException as e:
            logging.error(f"API request failed: {str(e)}")
            return ""
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse API response as JSON: {str(e)}")
            return ""
        except Exception as e:
            logging.error(f"Unexpected error when calling API: {str(e)}")
            return ""
    
    def test_formula_processing(self):
        """测试算式处理"""
        print("=== 测试算式处理 ===")
        test_cases = [
            (100, "(x+2)*3", 306),
            (50, "x/2", 25),
            (10, "x**2", 100),
            ("abc", "x+1", None)  # 错误情况
        ]
        
        for value, formula, expected in test_cases:
            try:
                x = float(value)
                formula_eval = formula.replace('x', str(x))
                result = eval(formula_eval)
                print(f"值: {value}, 公式: {formula}, 结果: {result}, 期望: {expected}")
            except:
                result = None
                print(f"值: {value}, 公式: {formula}, 结果: {result}, 期望: {expected}")
    
    def test_json_field_extraction(self):
        """测试JSON字段提取"""
        print("\n=== 测试JSON字段提取 ===")
        test_cases = [
            ('{"name": "张三", "age": 30, "city": "北京"}', "name|age", "张三|30"),
            ('{"msg": "hello", "code": 200}', "msg", "hello"),
            ('{"data": {"user": "test"}}', "data", "{'user': 'test'}"),
            ('invalid json', "name", None)  # 错误情况
        ]
        
        for value, fields, expected in test_cases:
            try:
                if isinstance(value, str):
                    data = json.loads(value)
                else:
                    data = value
                
                field_list = fields.split('|')
                results = []
                for field in field_list:
                    field = field.strip()
                    if field in data:
                        results.append(str(data[field]))
                    else:
                        results.append("")
                
                result = "|".join(results) if len(results) > 1 else results[0] if results else ""
                print(f"JSON: {value[:50]}..., 字段: {fields}, 结果: {result}, 期望: {expected}")
            except:
                result = None
                print(f"JSON: {value[:50]}..., 字段: {fields}, 结果: {result}, 期望: {expected}")
    
    def test_ai_formula(self):
        """测试AI公式生成"""
        print("\n=== 测试AI公式生成 ===")
        test_descriptions = [
            "计算A列和B列的和",
            "计算平均值",
            "统计大于100的数量"
        ]
        
        for desc in test_descriptions:
            prompt = f"""请根据以下描述，为Excel生成一个公式。
描述：{desc}
当前列名：测试列
当前行号：2

请只返回Excel公式，不要包含其他解释。公式应该以=开头。
例如：=A2+B2 或 =SUM(A2:C2) 等。"""
            
            print(f"描述: {desc}")
            result = self.callAPI(prompt)
            print(f"生成的公式: {result}")
            print()
    
    def test_ai_custom_task(self):
        """测试AI自定义任务"""
        print("\n=== 测试AI自定义任务 ===")
        test_cases = [
            ("这是一个测试文本，包含重要信息。", "提取关键信息"),
            ("用户反馈：产品很好用，但是价格有点贵。", "分析用户情感"),
            ("2024-06-15 10:30:00", "转换为友好的时间格式")
        ]
        
        for content, task in test_cases:
            prompt = f"""请根据以下任务描述处理给定的内容：
任务描述：{task}
待处理内容：{content}

请直接返回处理结果，不要包含其他解释。"""
            
            print(f"内容: {content}")
            print(f"任务: {task}")
            result = self.callAPI(prompt)
            print(f"处理结果: {result}")
            print()

def main():
    """主测试函数"""
    processor = TestProcessor()
    
    # 测试基本功能
    processor.test_formula_processing()
    processor.test_json_field_extraction()
    
    # 测试AI功能（需要网络连接）
    print("\n是否测试AI功能？(y/n): ", end="")
    choice = input().lower()
    if choice == 'y':
        processor.test_ai_formula()
        processor.test_ai_custom_task()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
