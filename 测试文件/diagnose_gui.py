#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI问题诊断脚本
"""

import sys
import os

def check_environment():
    """检查环境"""
    print("=== 环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {os.name}")
    print(f"当前目录: {os.getcwd()}")
    
    # 检查DISPLAY环境变量（对于Linux/macOS）
    display = os.environ.get('DISPLAY')
    if display:
        print(f"DISPLAY: {display}")
    else:
        print("DISPLAY: 未设置")
    
    print()

def test_tkinter_import():
    """测试tkinter导入"""
    print("=== 测试tkinter导入 ===")
    try:
        import tkinter as tk
        print("✓ tkinter导入成功")
        
        import tkinter.ttk as ttk
        print("✓ tkinter.ttk导入成功")
        
        return True
    except Exception as e:
        print(f"✗ tkinter导入失败: {e}")
        return False

def test_window_creation():
    """测试窗口创建"""
    print("=== 测试窗口创建 ===")
    try:
        import tkinter as tk
        
        print("创建根窗口...")
        root = tk.Tk()
        print("✓ 根窗口创建成功")
        
        print("设置窗口属性...")
        root.title("测试窗口")
        root.geometry("300x200")
        print("✓ 窗口属性设置成功")
        
        print("添加标签...")
        label = tk.Label(root, text="测试标签")
        label.pack()
        print("✓ 标签添加成功")
        
        print("更新窗口...")
        root.update()
        print("✓ 窗口更新成功")
        
        print("检查窗口状态...")
        print(f"窗口状态: {root.state()}")
        print(f"窗口几何: {root.geometry()}")
        
        # 立即销毁窗口
        print("销毁窗口...")
        root.destroy()
        print("✓ 窗口销毁成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 窗口创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mainloop():
    """测试主循环（短时间）"""
    print("=== 测试主循环 ===")
    try:
        import tkinter as tk
        
        root = tk.Tk()
        root.title("主循环测试")
        root.geometry("400x300")
        
        # 添加内容
        frame = tk.Frame(root, bg="lightblue")
        frame.pack(fill=tk.BOTH, expand=True)
        
        label = tk.Label(frame, text="主循环测试\n如果看到这个窗口说明GUI正常", 
                        font=("Arial", 14), bg="lightblue")
        label.pack(expand=True)
        
        # 自动关闭
        def auto_close():
            print("自动关闭窗口")
            root.quit()
            root.destroy()
        
        root.after(2000, auto_close)  # 2秒后关闭
        
        print("启动主循环（2秒后自动关闭）...")
        root.mainloop()
        print("✓ 主循环测试完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 主循环测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("GUI问题诊断工具")
    print("=" * 40)
    
    # 设置环境变量
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    
    # 检查环境
    check_environment()
    
    # 测试tkinter导入
    if not test_tkinter_import():
        print("tkinter导入失败，无法继续测试")
        return
    
    # 测试窗口创建
    if not test_window_creation():
        print("窗口创建失败，可能是显示问题")
        return
    
    # 测试主循环
    if not test_mainloop():
        print("主循环测试失败")
        return
    
    print("\n=== 诊断完成 ===")
    print("所有测试通过！GUI应该可以正常工作。")
    print("如果Excel应用界面仍然空白，可能是应用代码的问题。")

if __name__ == "__main__":
    main()
