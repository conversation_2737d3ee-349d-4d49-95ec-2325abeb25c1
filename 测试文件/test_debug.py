import traceback

try:
    import freemarker_skill as fs
    
    with open("debug_output.txt", "w", encoding="utf-8") as f:
        f.write("Starting test\n")
        
        f.write("\nTesting simple arithmetic expression\n")
        try:
            result1 = fs.freemarker_style_renderer("${index+1}", {"index": 0})
            f.write(f"Result 1: {result1}\n")
        except Exception as e:
            f.write(f"Error in test 1: {str(e)}\n")
            f.write(traceback.format_exc())
        
        f.write("\nTesting list index\n")
        try:
            template = """<#list items as item>${item_index+1}. ${item}</#list>"""
            data = {"items": ["apple", "banana", "cherry"]}
            result2 = fs.freemarker_style_renderer(template, data)
            f.write(f"Result 2: {result2}\n")
        except Exception as e:
            f.write(f"Error in test 2: {str(e)}\n")
            f.write(traceback.format_exc())
        
        f.write("\nTesting policy template\n")
        try:
            template3 = """您申请退保的保单信息如下：
<#list policyList as policy>${policy_index+1}.产品名称：${policy.产品名称}
✅被保人：${policy.被保人姓名}
✅交费金额：${policy.月保费}元/月（年交：${policy.年保费}元/年）
</#list>您看是这几份保单吗？"""

            data3 = {
                "policyList": [
                    {
                        "产品名称": "众安百万重疾险（旗舰版）",
                        "被保人姓名": "*塘军",
                        "月保费": "213.0",
                        "年保费": 2556
                    },
                    {
                        "产品名称": "优保关爱·百万医疗（加强版）",
                        "被保人姓名": "*塘军", 
                        "月保费": "0.99",
                        "年保费": 737.99
                    }
                ]
            }
            
            result3 = fs.freemarker_style_renderer(template3, data3)
            f.write(f"Result 3: {result3}\n")
        except Exception as e:
            f.write(f"Error in test 3: {str(e)}\n")
            f.write(traceback.format_exc())
    
    print("Test completed. Check debug_output.txt for results.")
    
except Exception as e:
    with open("debug_output.txt", "w", encoding="utf-8") as f:
        f.write(f"Fatal error: {str(e)}\n")
        f.write(traceback.format_exc()) 