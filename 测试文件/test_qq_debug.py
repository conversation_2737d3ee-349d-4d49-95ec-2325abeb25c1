from freemarker_skill import freemarker_style_renderer

# 定义我们需要测试的??条件表达式
condition = "降级对比图片链接??"

# 手动调用evaluate_condition，检查??操作符的处理逻辑
def evaluate_condition(condition, data_dict):
    print(f"条件字符串: '{condition}'")
    condition = condition.strip()
    
    # 1. 处理字面量true/false
    if condition == "true":
        print("发现字面量true")
        return True
    if condition == "false":
        print("发现字面量false")
        return False
        
    # 1.5 处理存在性检查 ?? 操作符
    if "??" in condition:
        print("发现??操作符")
        var_name = condition.replace("??", "").strip()
        print(f"变量名: '{var_name}'")
        # 处理嵌套路径 (a.b.c??)
        parts = var_name.split('.')
        print(f"路径部分: {parts}")
        current_val = data_dict
        try:
            exists = True
            for part in parts:
                if part.strip():  # 跳过空部分
                    if isinstance(current_val, dict) and part in current_val:
                        print(f"在当前字典中找到'{part}'")
                        current_val = current_val[part]
                        print(f"值为: {current_val}")
                    else:
                        print(f"在当前字典中未找到'{part}'")
                        exists = False
                        break
            # 变量存在且非None返回True
            result = exists and current_val is not None
            print(f"变量存在检查结果: {result}")
            return result
        except Exception as e:
            print(f"处理??操作符时发生错误: {e}")
            return False
    
    # 2. 处理变量存在性检查
    if condition in data_dict:
        print(f"直接在字典中找到变量'{condition}'")
        value = data_dict[condition]
        print(f"值为: {value}, 类型: {type(value)}")
        if value == "true":
            return True
        if value == "false":
            return False
        return bool(value)
    
    print(f"无法处理的条件: '{condition}'")
    return False

# 测试数据
test_data = {
    'test_var': 'hello',
    '降级对比图片链接': 'https://example.com/image.png'
}

# 测试??条件
print("=" * 50)
print("测试??条件:")
result = evaluate_condition(condition, test_data)
print(f"最终结果: {result}")
print("=" * 50)

# 测试FreeMarker模板渲染
template1 = "<#if test_var??>变量test_var存在</#if>"
template2 = "<#if missing_var??>变量missing_var不存在</#if>"
template3 = "<#if 降级对比图片链接??>变量降级对比图片链接存在</#if>"
template4 = '[{ "type": "news" }<#if 降级对比图片链接??>,{ "type": "image" }]</#if>'

# 打印输入数据和模板
print("\n输入数据:")
for key, value in test_data.items():
    print(f"  {key}: {value}")

print("\n模板测试结果:")
print(f"模板1: {template1}")
print(f"结果1: '{freemarker_style_renderer(template1, test_data)}'")

print(f"模板2: {template2}")
print(f"结果2: '{freemarker_style_renderer(template2, test_data)}'")

print(f"模板3: {template3}")
print(f"结果3: '{freemarker_style_renderer(template3, test_data)}'")

print(f"模板4: {template4}")
print(f"结果4: '{freemarker_style_renderer(template4, test_data)}'")

# 测试原始问题中的模板
original_template = '[{ "newsDescription": "降低保费，延续保障", "newsPicurl": "https://example.com/news.png", "newsTitle": "您可点此链接完成保障变更", "newsUrl": "https://example.com/link?policyNo=${保单号}", "type": "news" }<#if 降级对比图片链接??>,{ "imageUrl": "${降级对比图片链接}", "type": "image" }]</#if>'

original_data = {
    '保单号': 'POLICY123456',
    '降级对比图片链接': 'https://example.com/compare.png'
}

print("\n原始问题模板测试:")
print(f"结果: '{freemarker_style_renderer(original_template, original_data)}'") 