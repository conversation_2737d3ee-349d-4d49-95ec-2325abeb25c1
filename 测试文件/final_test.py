def test_condition(condition, data):
    """测试条件表达式"""
    print(f"测试条件: '{condition}'")

    # 处理字面量
    if condition == "true":
        return True
    if condition == "false":
        return False
        
    # 处理变量检查
    if condition in data:
        value = data[condition]
        if value == "true":
            return True
        if value == "false":
            return False
        return bool(value)
    
    # 处理等于比较
    if " == " in condition:
        parts = condition.split(" == ")
        left = parts[0].strip()
        right = parts[1].strip()
        
        # 获取左值
        if left in data:
            left_value = data[left]
        else:
            left_value = None
            
        # 获取右值
        if right == "true":
            right_value = True
        elif right == "false":
            right_value = False
        elif right.startswith('"') and right.endswith('"'):
            right_value = right[1:-1]
        elif right in data:
            right_value = data[right]
        else:
            right_value = right
            
        return left_value == right_value
    
    # 处理AND条件
    if " && " in condition:
        parts = condition.split(" && ")
        left = parts[0].strip()
        right = parts[1].strip()
        
        left_result = test_condition(left, data)
        if not left_result:
            return False
            
        right_result = test_condition(right, data)
        return left_result and right_result
    
    # 默认返回False
    return False

# 测试原始输入
template = """
<#if user_insurance_flg == "normal" && user_allow_retention_flg>
条件生效: normal+retention
</#if>
"""

# 测试数据
data = {
    'user_insurance_flg': 'normal',
    'user_allow_retention_flg': True,
    'user_insurance_size': 7
}

# 测试条件表达式
condition = 'user_insurance_flg == "normal" && user_allow_retention_flg'
result = test_condition(condition, data)
print(f"条件结果: {result}")

# 模拟解析过程
if result:
    print("模板输出:\n条件生效: normal+retention")
else:
    print("模板输出: (空)") 