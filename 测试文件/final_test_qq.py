import re
import json

def evaluate_condition(condition, data_dict):
    condition = condition.strip()
    
    # 处理??操作符
    if "??" in condition:
        var_name = condition.replace("??", "").strip()
        
        # 检查变量是否存在
        if var_name in data_dict:
            return True
        else:
            return False
            
    return False

def process_if_block(if_block, data_dict):
    # 使用新的正则表达式模式
    if_match = re.search(r"<#if\s+([^>]+)>", if_block)
    if not if_match:
        return if_block
    
    condition = if_match.group(1).strip()
    
    # 提取内容
    content_match = re.search(r"<#if\s+[^>]+>(.*?)</#if>", if_block, re.DOTALL)
    if not content_match:
        return if_block
    
    content = content_match.group(1)
    
    # 评估条件
    if evaluate_condition(condition, data_dict):
        return content
    else:
        return ""

def process_template(template, data_dict):
    # 查找所有if块
    if_blocks = []
    pos = 0
    
    while pos < len(template):
        if_match = re.search(r"<#if\s+[^>]+>.*?</#if>", template[pos:], re.DOTALL)
        if not if_match:
            break
        
        start = pos + if_match.start()
        end = pos + if_match.end()
        if_blocks.append((start, end, if_match.group(0)))
        
        pos = end
    
    # 从后向前处理if块，避免索引错位
    result = template
    for start, end, if_block in reversed(if_blocks):
        replacement = process_if_block(if_block, data_dict)
        result = result[:start] + replacement + result[end:]
    
    return result

# 测试数据
test_data = {
    'test_var': 'hello',
    '降级对比图片链接': 'https://example.com/image.png'
}

# 测试模板
test_templates = [
    "<#if test_var??>test_var存在</#if>",
    "<#if missing_var??>missing_var存在</#if>",
    "<#if 降级对比图片链接??>降级对比图片链接存在</#if>",
    '[{ "type": "news" }<#if 降级对比图片链接??>,{ "type": "image" }]</#if>'
]

# 运行测试
print("=" * 50)
print("测试??操作符:")
for i, template in enumerate(test_templates):
    result = process_template(template, test_data)
    print(f"模板 {i+1}: '{template}'")
    print(f"结果 {i+1}: '{result}'")
    print("-" * 30)

# 测试问题模板
problem_template = '[{ "newsDescription": "降低保费，延续保障", "newsPicurl": "https://example.com/news.png", "newsTitle": "您可点此链接完成保障变更", "newsUrl": "https://example.com/link", "type": "news" }<#if 降级对比图片链接??>,{ "imageUrl": "${降级对比图片链接}", "type": "image" }]</#if>'

print("\n原始问题模板测试:")
result = process_template(problem_template, test_data)
print(f"模板: '{problem_template}'")
print(f"结果: '{result}'")
print("=" * 50)

# 测试数据中不包含变量的情况
no_var_data = {}
print("\n变量不存在的情况测试:")
result = process_template(problem_template, no_var_data)
print(f"结果: '{result}'")
print("=" * 50)

# 创建一个文本文件保存结果
with open('test_results.txt', 'w', encoding='utf-8') as f:
    f.write(f"原始模板: {problem_template}\n")
    f.write(f"变量存在时结果: {process_template(problem_template, test_data)}\n")
    f.write(f"变量不存在时结果: {process_template(problem_template, no_var_data)}\n") 