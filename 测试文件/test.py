@tool
def process_faq_data(intent_id="", faq_intent_list=[], faq_list=[], script_list=[]):

    # 合并faq_intent_list和faq_list
    merged_faq_list = []

    # 添加faq_intent_list中的项目
    for item in faq_intent_list:
        merged_faq_list.append(item)

    # 添加faq_list中的项目
    for item in faq_list:
        merged_faq_list.append(item)

    # 按faqNo去重，保留score更高的
    unique_faq_list = []

    for item in merged_faq_list:
        current_faq_no = item.get("faqNo", "")
        current_score = item.get("score", 0)

        # 检查是否已存在相同faqNo的项目
        found_index = -1
        for i in range(len(unique_faq_list)):
            existing_faq_no = unique_faq_list[i].get("faqNo", "")
            if existing_faq_no == current_faq_no:
                found_index = i
                break

        if found_index == -1:
            # 不存在，直接添加
            unique_faq_list.append(item)
        else:
            # 已存在，比较score
            existing_score = unique_faq_list[found_index].get("score", 0)
            if current_score > existing_score:
                # 当前score更高，替换
                unique_faq_list.pop(found_index)
                unique_faq_list.insert(found_index, item)

    # 按score从高到低排序（冒泡排序）
    n = len(unique_faq_list)
    for i in range(n):
        for j in range(0, n - i - 1):
            score1 = unique_faq_list[j].get("score", 0)
            score2 = unique_faq_list[j + 1].get("score", 0)
            if score1 < score2:
                # 交换位置
                item1 = unique_faq_list.pop(j)
                item2 = unique_faq_list.pop(j)  # 注意：pop(j)后，原来的j+1位置变成了j
                unique_faq_list.insert(j, item2)
                unique_faq_list.insert(j + 1, item1)

    # 根据intent_id查找对应的intentCategory和level
    target_intent_category = ""
    target_level = ""

    for script in script_list:
        script_id = script.get("id", "")
        if script_id == intent_id:
            target_intent_category = script.get("intentCategory", "")
            target_level = script.get("level", "")
            break

    # 找到相同intentCategory的所有script
    same_category_scripts = []
    for script in script_list:
        script_category = script.get("intentCategory", "")
        if script_category == target_intent_category and target_intent_category != "":
            same_category_scripts.append(script)

    # 获取原话术
    intent_script = ""
    if intent_id.startswith("FAQ-"):
        for faq in unique_faq_list:
            faq_answer = faq.get("faqAnswer", "")
            if faq_answer.startswith(intent_id):
                intent_script = faq_answer
                break
    else:
        for script in same_category_scripts:
            script_id = script.get("id", "")
            if script_id == intent_id:
                intent_script = script.get("content", "")
                break

    # 构建返回结果
    result = {
        "faqList": unique_faq_list,
        "intentCategory": target_intent_category,
        "level": target_level,
        "scriptList": same_category_scripts,
        "intent_script": intent_script
    }

    return result

@tool
def main(date=''):
    return {
      "key": "JYMQ:dailyreport:" + date,
      "summaryKey": "JYMQ:dailySummary:" + date,
      "expireTime": "86400",
      #date 为 2025-06-07 取 0607
      "dateString": date[-5:].replace("-", "")
    }