#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
import os

print("开始测试...")

# 设置环境变量
os.environ['TK_SILENCE_DEPRECATION'] = '1'

try:
    print("创建窗口...")
    root = tk.Tk()
    root.title("简单测试")
    root.geometry("400x300")
    
    print("添加标签...")
    label = tk.Label(root, text="Hello World!", font=("Arial", 20))
    label.pack(expand=True)
    
    print("显示窗口...")
    root.update()
    root.deiconify()
    root.lift()
    
    print("启动主循环...")
    root.mainloop()
    
    print("程序结束")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
