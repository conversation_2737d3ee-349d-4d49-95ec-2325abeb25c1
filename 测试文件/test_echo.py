import sys

# 简单的测试输出
print("测试输出")
print("Python版本:", sys.version)

# 尝试正则表达式
import re
pattern = r"<#if\s+(.*?)>(.*?)</#if>"
test_string = "<#if true>内容</#if>"
matches = re.findall(pattern, test_string, re.DOTALL)
print("正则匹配结果:", matches)

# 测试一个简单的函数
def test_bool():
    print("布尔测试函数")
    print("True:", bool(True))
    print("False:", bool(False))
    return bool(True)

result = test_bool()
print("函数结果:", result)

print("测试完成") 