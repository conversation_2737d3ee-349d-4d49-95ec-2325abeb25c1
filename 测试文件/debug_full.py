import re
import json

# 处理if条件块
def process_if_block(if_block, data_dict):
    # 提取if条件
    if_match = re.search(r"<#if\s+(.+?)>", if_block, re.DOTALL)
    if not if_match:
        print("没有找到if条件")
        return ""
        
    if_condition = if_match.group(1).strip()
    print(f"提取的if条件是: '{if_condition}'")
    
    # 创建一个结构表示if-elseif-else块
    blocks = []
    if_end = if_match.end()
    remaining = if_block[if_end:]
    
    # 查找endif
    endif_pos = remaining.rfind("</#if>")
    if endif_pos == -1:
        print("没有找到匹配的endif标签")
        return if_block  # 返回原始内容
    
    # 提取if语句中的内容
    if_content = remaining[:endif_pos]
    print(f"if中的内容: '{if_content}'")
    
    # 将if部分添加到块列表
    blocks.append(("if", if_condition, if_content))
    
    # 评估条件并返回相应内容
    for block_type, condition, content in blocks:
        result = evaluate_condition(condition, data_dict)
        print(f"条件 '{condition}' 的评估结果: {result}")
        if result:
            print(f"返回内容: '{content}'")
            return content
    
    print("所有条件都不满足，返回空字符串")
    return ""  # 如果没有条件满足

# 评估条件的函数
def evaluate_condition(condition, data_dict):
    print(f"评估条件: '{condition}'")
    condition = condition.strip()
    
    # 1. 处理字面量true/false
    if condition == "true":
        print("条件是字面量true")
        return True
    if condition == "false":
        print("条件是字面量false")
        return False
        
    # 1.5 处理存在性检查 ?? 操作符
    if "??" in condition:
        print("检测到??操作符")
        var_name = condition.replace("??", "").strip()
        print(f"变量名: '{var_name}'")
        
        # 处理嵌套路径 (a.b.c??)
        parts = var_name.split('.')
        print(f"路径部分: {parts}")
        current_val = data_dict
        
        try:
            exists = True
            for part in parts:
                if part.strip():  # 跳过空部分
                    print(f"检查部分: '{part}'")
                    if isinstance(current_val, dict) and part in current_val:
                        print(f"在字典中找到了'{part}'")
                        current_val = current_val[part]
                        print(f"变量值: {current_val}")
                    else:
                        print(f"在字典中没有找到'{part}'")
                        exists = False
                        break
            # 变量存在且非None返回True
            result = exists and current_val is not None
            print(f"变量{var_name}存在检查结果: {result}")
            return result
        except Exception as e:
            print(f"处理??时出错: {e}")
            return False
            
    # 2. 其他条件处理（此处简化）
    print(f"未处理的条件类型: '{condition}'")
    return False

# 处理变量替换
def process_variables(text, data_dict):
    print(f"处理变量替换，文本长度: {len(text)}")
    
    # 使用正则表达式找出所有的${variable}模式
    var_pattern = r"\${([^}]+)}"
    
    # 查找所有变量占位符
    matches = list(re.finditer(var_pattern, text))
    print(f"找到{len(matches)}个变量占位符")
    
    # 如果没有匹配，直接返回原文本
    if not matches:
        return text
    
    # 从后向前替换，避免索引错位问题
    for match in reversed(matches):
        var_expr = match.group(1).strip()
        print(f"变量表达式: '{var_expr}'")
        
        # 简单变量替换（没有实现复杂表达式）
        if var_expr in data_dict:
            replacement = str(data_dict[var_expr])
            print(f"变量'{var_expr}'的值: '{replacement}'")
        else:
            print(f"变量'{var_expr}'不存在于数据字典中")
            replacement = ""
            
        # 替换变量
        start, end = match.span()
        text = text[:start] + replacement + text[end:]
        
    return text

# 主函数
def freemarker_style_renderer(template, data_dict):
    print(f"开始处理模板，长度: {len(template)}")
    print(f"数据字典: {json.dumps(data_dict, ensure_ascii=False, indent=2)}")
    
    # 处理if标签
    if_pattern = r"<#if\s+.+?>.*?</#if>"
    
    # 查找所有if块
    matches = list(re.finditer(if_pattern, template, re.DOTALL))
    print(f"找到{len(matches)}个if块")
    
    # 如果没有if标签，只处理变量替换
    if not matches:
        print("没有if块，只处理变量替换")
        return process_variables(template, data_dict)
    
    # 从后向前处理if块，避免索引错位问题
    for match in reversed(matches):
        if_block = match.group(0)
        print(f"处理if块: '{if_block}'")
        
        # 使用process_if_block函数处理if块
        replacement = process_if_block(if_block, data_dict)
        print(f"if块处理后的结果: '{replacement}'")
        
        # 替换if块
        start, end = match.span()
        template = template[:start] + replacement + template[end:]
    
    # 最后处理变量替换
    print("处理变量替换")
    result = process_variables(template, data_dict)
    
    print(f"最终结果: '{result}'")
    return result

# 测试数据
test_data = {
    'test_var': 'hello',
    '降级对比图片链接': 'https://example.com/image.png',
    '保单号': 'POLICY123456'
}

# 测试模板
test_templates = [
    "<#if test_var??>变量test_var存在</#if>",
    "<#if missing_var??>变量missing_var不存在</#if>",
    "<#if 降级对比图片链接??>变量降级对比图片链接存在</#if>",
    '[{ "type": "news" }<#if 降级对比图片链接??>,{ "type": "image" }]</#if>',
    '[{ "newsDescription": "降低保费，延续保障", "newsPicurl": "https://example.com/news.png", "newsTitle": "您可点此链接完成保障变更", "newsUrl": "https://example.com/link?policyNo=${保单号}", "type": "news" }<#if 降级对比图片链接??>,{ "imageUrl": "${降级对比图片链接}", "type": "image" }]</#if>'
]

# 执行测试
print("=" * 50)
print("开始测试:")
for i, template in enumerate(test_templates):
    print("-" * 30)
    print(f"测试模板 {i+1}: '{template}'")
    result = freemarker_style_renderer(template, test_data)
    print(f"结果 {i+1}: '{result}'")
    print("-" * 30)
print("=" * 50) 