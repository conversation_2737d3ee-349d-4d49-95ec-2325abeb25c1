#!/usr/bin/env python3
import json
import gzip
import time
import sys
import base64

def test_serialization_methods():
    """测试不同序列化方法的效果"""
    
    # 模拟您的大数据结构
    test_data = {
        'policies': [
            {
                'commodityName': f'众安百万医疗险{i}',
                'totalPremium': '1200.00',
                'premiumPayment': '100.00',
                'orderDate': '2023-01-15',
                'holderName': '张三',
                'policyName': '张三',
                'policyNo': f'ZA202301150{i:03d}',
                'isFree': i % 5 == 0,
                'downGradeFlag': 'Y',
                'operationsTypeName': '医疗险',
                'marsOrderNo': f'MARS{i:06d}',
                'relationToHolder': '本人',
                'cancelApplyStatus': '未申请'
            } for i in range(300)  # 300个保单
        ],
        'metadata': {
            'total_count': 300,
            'free_count': 60,
            'non_free_count': 240,
            'timestamp': '2024-01-15T10:30:00Z',
            'description': '包含大量保单数据的测试用例，用于验证序列化效果。' * 5
        }
    }
    
    print('🔍 数据结构分析:')
    print(f'保单数量: {len(test_data["policies"])}')
    print(f'预估大小: ~{len(str(test_data))} 字符')
    print('=' * 60)
    
    results = {}
    
    # 1. 原始JSON方法（您当前使用的）
    start = time.time()
    json_standard = json.dumps(test_data)
    json_time = (time.time() - start) * 1000
    json_size = len(json_standard.encode('utf-8'))
    results['原始JSON'] = {'size': json_size, 'time': json_time}
    
    # 2. 紧凑JSON方法
    start = time.time()
    json_compact = json.dumps(test_data, separators=(',', ':'))
    json_compact_time = (time.time() - start) * 1000
    json_compact_size = len(json_compact.encode('utf-8'))
    results['紧凑JSON'] = {'size': json_compact_size, 'time': json_compact_time}
    
    # 3. JSON + Gzip压缩
    start = time.time()
    json_gzip = gzip.compress(json_compact.encode('utf-8'))
    gzip_time = (time.time() - start) * 1000
    gzip_size = len(json_gzip)
    results['JSON+Gzip'] = {'size': gzip_size, 'time': gzip_time}
    
    # 4. JSON + Gzip + Base64（推荐方案）
    start = time.time()
    json_gzip_b64 = base64.b64encode(json_gzip).decode('ascii')
    b64_time = (time.time() - start) * 1000
    b64_size = len(json_gzip_b64)
    results['JSON+Gzip+B64'] = {'size': b64_size, 'time': gzip_time + b64_time}
    
    # 输出结果
    print('📊 序列化方法对比:')
    print('-' * 60)
    
    for method, data in results.items():
        size = data['size']
        time_ms = data['time']
        saving = (1 - size/json_size) * 100 if method != '原始JSON' else 0
        
        print(f'{method:12} | {size:8,} bytes | {time_ms:6.2f}ms | 节省: {saving:5.1f}%')
    
    print('-' * 60)
    
    # 推荐方案
    best_compression = min(results.items(), key=lambda x: x[1]['size'])
    print(f'🏆 最佳压缩: {best_compression[0]} (节省 {(1-best_compression[1]["size"]/json_size)*100:.1f}%)')
    
    return results

def convert_result_to_string_optimized(result):
    """优化的序列化函数 - 推荐方案"""
    import gzip
    import json
    import base64
    import time
    
    start_time = time.time()
    
    # 1. 紧凑JSON序列化（去除不必要的空格）
    json_str = json.dumps(result, ensure_ascii=False, separators=(',', ':'))
    
    # 2. Gzip压缩
    compressed = gzip.compress(json_str.encode('utf-8'))
    
    # 3. Base64编码（便于Redis存储）
    encoded = base64.b64encode(compressed).decode('ascii')
    
    processing_time = (time.time() - start_time) * 1000
    
    original_size = len(json_str.encode('utf-8'))
    final_size = len(encoded)
    
    return {
        "data_string": encoded,
        "method": "json_gzip_b64",
        "original_size": original_size,
        "final_size": final_size,
        "compression_ratio": final_size / original_size,
        "space_saving_percent": (1 - final_size / original_size) * 100,
        "processing_time_ms": processing_time
    }

def decompress_data(compressed_data):
    """解压缩数据"""
    import gzip
    import base64
    import json
    
    try:
        # Base64解码 -> Gzip解压 -> JSON解析
        compressed = base64.b64decode(compressed_data.encode('ascii'))
        json_str = gzip.decompress(compressed).decode('utf-8')
        return json.loads(json_str)
    except Exception as e:
        print(f"解压缩失败: {e}")
        return None

if __name__ == "__main__":
    print("🚀 序列化优化测试")
    print("=" * 60)
    
    # 运行测试
    results = test_serialization_methods()
    
    print("\n💡 优化建议:")
    print("1. 使用紧凑JSON格式 (separators=(',', ':')) 可节省 5-10% 空间")
    print("2. 添加Gzip压缩可节省 60-80% 空间")
    print("3. 对于Redis存储，建议使用 JSON+Gzip+Base64 方案")
    print("4. 如果追求极致性能，可考虑 MessagePack 或 Pickle")
    
    print("\n🔧 实施方案:")
    print("将您的 convert_result_to_string 函数替换为:")
    print("convert_result_to_string_optimized(result)")
