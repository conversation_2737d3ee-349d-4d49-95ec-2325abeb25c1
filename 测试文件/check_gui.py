#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查GUI是否正常显示的测试脚本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import time

def test_basic_gui():
    """测试基本GUI功能"""
    print("测试基本GUI功能...")
    
    # 设置环境变量
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    
    try:
        # 创建窗口
        root = tk.Tk()
        root.title("GUI测试窗口")
        root.geometry("600x400")
        
        # 添加内容
        main_frame = ttk.Frame(root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title = ttk.Label(main_frame, text="GUI测试成功！", 
                         font=("Arial", 20, "bold"), foreground="green")
        title.pack(pady=20)
        
        # 说明文本
        info = ttk.Label(main_frame, text="如果您看到这个窗口，说明GUI可以正常显示", 
                        font=("Arial", 12))
        info.pack(pady=10)
        
        # 测试按钮
        def test_button():
            messagebox.showinfo("测试", "按钮功能正常！")
        
        btn = ttk.Button(main_frame, text="点击测试", command=test_button)
        btn.pack(pady=10)
        
        # 测试输入框
        entry_frame = ttk.Frame(main_frame)
        entry_frame.pack(pady=10)
        
        ttk.Label(entry_frame, text="测试输入:").pack(side=tk.LEFT)
        entry = ttk.Entry(entry_frame, width=30)
        entry.pack(side=tk.LEFT, padx=5)
        entry.insert(0, "输入框正常工作")
        
        # 测试下拉框
        combo_frame = ttk.Frame(main_frame)
        combo_frame.pack(pady=10)
        
        ttk.Label(combo_frame, text="测试下拉框:").pack(side=tk.LEFT)
        combo = ttk.Combobox(combo_frame, values=["选项1", "选项2", "选项3"])
        combo.pack(side=tk.LEFT, padx=5)
        combo.set("选项1")
        
        # 测试进度条
        progress = ttk.Progressbar(main_frame, mode='determinate', length=300)
        progress.pack(pady=10)
        progress['value'] = 70
        
        # 状态信息
        status = ttk.Label(main_frame, text="所有组件正常显示", 
                          foreground="blue")
        status.pack(pady=10)
        
        # 关闭按钮
        def close_app():
            print("GUI测试完成")
            root.destroy()
        
        close_btn = ttk.Button(main_frame, text="关闭", command=close_app)
        close_btn.pack(pady=20)
        
        # 确保窗口可见
        root.update()
        root.deiconify()
        root.lift()
        root.focus_force()
        
        print("GUI窗口已创建，启动主循环...")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_app():
    """测试Excel应用是否能正常启动"""
    print("\n测试Excel应用启动...")
    
    try:
        from excel_app_v2 import ExcelProcessorV2
        
        root = tk.Tk()
        root.withdraw()  # 先隐藏窗口
        
        app = ExcelProcessorV2(root)
        
        # 显示窗口
        root.deiconify()
        root.lift()
        root.focus_force()
        
        print("Excel应用创建成功！")
        
        # 自动关闭（用于测试）
        def auto_close():
            print("自动关闭Excel应用")
            root.destroy()
        
        root.after(3000, auto_close)  # 3秒后自动关闭
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"Excel应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("GUI显示测试程序")
    print("=" * 50)
    
    # 测试1：基本GUI
    print("\n1. 测试基本GUI功能...")
    if test_basic_gui():
        print("✓ 基本GUI测试通过")
    else:
        print("✗ 基本GUI测试失败")
        return
    
    # 测试2：Excel应用
    print("\n2. 测试Excel应用...")
    if test_excel_app():
        print("✓ Excel应用测试通过")
    else:
        print("✗ Excel应用测试失败")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
