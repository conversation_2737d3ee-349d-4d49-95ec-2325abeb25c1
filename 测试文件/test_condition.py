def test_evaluate_condition(condition, data):
    """测试条件评估函数"""
    condition = condition.strip()
    
    # 处理字面量true/false
    if condition == "true":
        print(f"条件 '{condition}' 是字面量true, 返回True")
        return True
    if condition == "false":
        print(f"条件 '{condition}' 是字面量false, 返回False")
        return False
        
    # 处理变量存在性检查
    if condition in data:
        # 直接变量引用 (e.g., <#if user_allow_retention_flg>)
        value = data[condition]
        print(f"条件 '{condition}' 在数据中, 值为 {value}, 类型为 {type(value)}")
        # 确保字符串布尔值转换为实际布尔值
        if value == "true":
            print(f"  字符串'true', 返回True")
            return True
        if value == "false":
            print(f"  字符串'false', 返回False")
            return False
        # 其他类型按Python规则转为布尔值
        result = bool(value)
        print(f"  转换为布尔值: {result}")
        return result
    
    # 等于比较 (e.g., <#if user_allow_retention_flg == true>)
    if " == " in condition:
        parts = condition.split(" == ")
        left = parts[0].strip()
        right = parts[1].strip()
        
        print(f"条件 '{condition}' 是等于比较")
        
        # 获取左侧值
        if left in data:
            left_value = data[left]
            print(f"  左侧 '{left}' 值为 {left_value}, 类型为 {type(left_value)}")
        else:
            left_value = None
            print(f"  左侧 '{left}' 不在数据中, 使用None")
            
        # 右侧为字面量?
        if right == "true":
            right_value = True
            print(f"  右侧 '{right}' 是字面量true")
        elif right == "false":
            right_value = False
            print(f"  右侧 '{right}' 是字面量false")
        elif right in data:
            right_value = data[right]
            print(f"  右侧 '{right}' 在数据中, 值为 {right_value}, 类型为 {type(right_value)}")
        else:
            # 尝试字符串值
            right_value = right
            print(f"  右侧 '{right}' 不在数据中, 使用字符串值")
        
        # 进行实际比较
        result = left_value == right_value
        print(f"  比较结果: {result}")
        return result
        
    # 不等于比较
    if " != " in condition:
        parts = condition.split(" != ")
        left = parts[0].strip()
        right = parts[1].strip()
        
        print(f"条件 '{condition}' 是不等于比较")
        
        # 获取左侧值
        if left in data:
            left_value = data[left]
            print(f"  左侧 '{left}' 值为 {left_value}, 类型为 {type(left_value)}")
        else:
            left_value = None
            print(f"  左侧 '{left}' 不在数据中, 使用None")
            
        # 右侧为字面量?
        if right == "true":
            right_value = True
            print(f"  右侧 '{right}' 是字面量true")
        elif right == "false":
            right_value = False
            print(f"  右侧 '{right}' 是字面量false")
        elif right in data:
            right_value = data[right]
            print(f"  右侧 '{right}' 在数据中, 值为 {right_value}, 类型为 {type(right_value)}")
        else:
            # 尝试字符串值
            right_value = right
            print(f"  右侧 '{right}' 不在数据中, 使用字符串值")
        
        # 进行实际比较
        result = left_value != right_value
        print(f"  比较结果: {result}")
        return result
    
    # 默认为false
    print(f"条件 '{condition}' 不符合已知模式, 返回False")
    return False

print("\n--- 测试条件计算 ---")

# 测试数据
test_data = {
    "user_allow_retention_flg": True,
    "string_true": "true",
    "string_false": "false",
    "number": 123,
    "zero": 0,
    "empty_string": "",
    "non_empty_string": "abc"
}

# 测试用例
test_cases = [
    "true",
    "false",
    "user_allow_retention_flg",
    "string_true",
    "string_false",
    "number",
    "zero",
    "empty_string",
    "non_empty_string",
    "non_existent_var",
    "user_allow_retention_flg == true",
    "user_allow_retention_flg == false",
    "string_true == true",
    "string_false == false",
    "non_existent_var == true",
    "user_allow_retention_flg != false",
    "user_allow_retention_flg != true"
]

# 执行测试
for test_case in test_cases:
    print(f"\n测试: '{test_case}'")
    result = test_evaluate_condition(test_case, test_data)
    print(f"最终结果: {result}")

print("\n测试完成！") 