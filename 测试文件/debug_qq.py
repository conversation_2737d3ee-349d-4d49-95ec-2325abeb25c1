import re

def evaluate_condition(condition, data_dict):
    print(f"评估条件: '{condition}'")
    condition = condition.strip()
    
    # 处理??操作符
    if "??" in condition:
        print("检测到??操作符")
        var_name = condition.replace("??", "").strip()
        print(f"变量名: '{var_name}'")
        
        # 检查变量是否存在
        if var_name in data_dict:
            print(f"变量'{var_name}'存在，值为: {data_dict[var_name]}")
            return True
        else:
            print(f"变量'{var_name}'不存在")
            return False
    
    print(f"未处理的条件类型: '{condition}'")
    return False

# 简单if块处理
def process_if_block(if_block, data_dict):
    if_match = re.search(r"<#if\s+(.+?)>", if_block)
    if not if_match:
        print("未找到if条件")
        return if_block
    
    condition = if_match.group(1).strip()
    print(f"提取的条件: '{condition}'")
    
    # 提取if内容（简化版）
    content_match = re.search(r"<#if\s+.+?>(.*?)</#if>", if_block, re.DOTALL)
    if not content_match:
        print("未找到if内容")
        return if_block
    
    content = content_match.group(1)
    print(f"if内容: '{content}'")
    
    # 评估条件
    if evaluate_condition(condition, data_dict):
        print("条件为真，返回内容")
        return content
    else:
        print("条件为假，返回空字符串")
        return ""

# 测试数据
test_data = {
    'test_var': 'hello',
    '降级对比图片链接': 'https://example.com/image.png'
}

# 测试模板 
test_templates = [
    "<#if test_var??>test_var存在</#if>",
    "<#if missing_var??>missing_var存在</#if>",
    "<#if 降级对比图片链接??>降级对比图片链接存在</#if>"
]

# 测试运行
print("=" * 50)
print("简化版条件处理测试:")
for i, template in enumerate(test_templates):
    print("-" * 30)
    print(f"模板 {i+1}: '{template}'")
    result = process_if_block(template, test_data)
    print(f"结果 {i+1}: '{result}'")

# 原始问题中的模板测试
print("\n" + "=" * 50)
problem_template = '<#if 降级对比图片链接??>,{ "imageUrl": "${降级对比图片链接}", "type": "image" }</#if>'
print(f"问题模板: '{problem_template}'")
result = process_if_block(problem_template, test_data)
print(f"结果: '{result}'")
print("=" * 50) 