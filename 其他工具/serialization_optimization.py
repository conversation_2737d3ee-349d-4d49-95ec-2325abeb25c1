#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
序列化优化方案对比
比较不同序列化方法的空间效率和性能
"""

import json
import pickle
import gzip
import bz2
import lzma
import time
import sys
from typing import Any, Dict, List
import msgpack
import orjson
import ujson

class SerializationOptimizer:
    """序列化优化器"""
    
    def __init__(self):
        self.methods = {
            'json_standard': self._json_standard,
            'json_compact': self._json_compact,
            'orjson': self._orjson,
            'ujson': self._ujson,
            'pickle': self._pickle,
            'msgpack': self._msgpack,
            'json_gzip': self._json_gzip,
            'json_bz2': self._json_bz2,
            'json_lzma': self._json_lzma,
            'pickle_gzip': self._pickle_gzip,
            'msgpack_gzip': self._msgpack_gzip,
        }
    
    def _json_standard(self, data):
        """标准JSON序列化"""
        return json.dumps(data, ensure_ascii=False)
    
    def _json_compact(self, data):
        """紧凑JSON序列化（无空格）"""
        return json.dumps(data, ensure_ascii=False, separators=(',', ':'))
    
    def _orjson(self, data):
        """orjson序列化（更快的JSON库）"""
        try:
            return orjson.dumps(data).decode('utf-8')
        except:
            return self._json_compact(data)
    
    def _ujson(self, data):
        """ujson序列化"""
        try:
            return ujson.dumps(data, ensure_ascii=False)
        except:
            return self._json_compact(data)
    
    def _pickle(self, data):
        """Pickle序列化"""
        return pickle.dumps(data)
    
    def _msgpack(self, data):
        """MessagePack序列化"""
        try:
            return msgpack.packb(data)
        except:
            return self._pickle(data)
    
    def _json_gzip(self, data):
        """JSON + Gzip压缩"""
        json_str = self._json_compact(data)
        return gzip.compress(json_str.encode('utf-8'))
    
    def _json_bz2(self, data):
        """JSON + BZ2压缩"""
        json_str = self._json_compact(data)
        return bz2.compress(json_str.encode('utf-8'))
    
    def _json_lzma(self, data):
        """JSON + LZMA压缩"""
        json_str = self._json_compact(data)
        return lzma.compress(json_str.encode('utf-8'))
    
    def _pickle_gzip(self, data):
        """Pickle + Gzip压缩"""
        pickle_data = self._pickle(data)
        return gzip.compress(pickle_data)
    
    def _msgpack_gzip(self, data):
        """MessagePack + Gzip压缩"""
        msgpack_data = self._msgpack(data)
        return gzip.compress(msgpack_data)
    
    def benchmark_serialization(self, data, iterations=100):
        """基准测试所有序列化方法"""
        results = {}
        
        print(f"🔍 测试数据大小估算: {sys.getsizeof(data)} bytes")
        print("=" * 80)
        
        for method_name, method_func in self.methods.items():
            try:
                # 性能测试
                start_time = time.time()
                for _ in range(iterations):
                    serialized = method_func(data)
                avg_time = (time.time() - start_time) / iterations * 1000  # ms
                
                # 大小测试
                if isinstance(serialized, str):
                    size = len(serialized.encode('utf-8'))
                else:
                    size = len(serialized)
                
                results[method_name] = {
                    'size': size,
                    'time_ms': avg_time,
                    'data': serialized
                }
                
                print(f"✅ {method_name:15} | 大小: {size:8,} bytes | 时间: {avg_time:6.2f}ms")
                
            except Exception as e:
                print(f"❌ {method_name:15} | 错误: {str(e)}")
                results[method_name] = {'error': str(e)}
        
        return results
    
    def get_best_method(self, results, priority='size'):
        """获取最佳方法"""
        valid_results = {k: v for k, v in results.items() if 'error' not in v}
        
        if not valid_results:
            return None
        
        if priority == 'size':
            best = min(valid_results.items(), key=lambda x: x[1]['size'])
        elif priority == 'speed':
            best = min(valid_results.items(), key=lambda x: x[1]['time_ms'])
        else:  # balanced
            # 综合评分：大小权重0.7，速度权重0.3
            def score(item):
                size_norm = item[1]['size'] / max(r['size'] for r in valid_results.values())
                time_norm = item[1]['time_ms'] / max(r['time_ms'] for r in valid_results.values())
                return size_norm * 0.7 + time_norm * 0.3
            
            best = min(valid_results.items(), key=score)
        
        return best

def convert_result_to_string_optimized(result, method='auto'):
    """优化的结果转换函数"""
    optimizer = SerializationOptimizer()
    
    if method == 'auto':
        # 自动选择最佳方法
        benchmark_results = optimizer.benchmark_serialization(result, iterations=10)
        best_method = optimizer.get_best_method(benchmark_results, priority='size')
        
        if best_method:
            method_name, method_data = best_method
            print(f"🎯 自动选择最佳方法: {method_name}")
            print(f"📊 大小: {method_data['size']:,} bytes, 时间: {method_data['time_ms']:.2f}ms")
            
            return {
                "data_string": method_data['data'],
                "method": method_name,
                "size": method_data['size'],
                "compression_ratio": method_data['size'] / len(json.dumps(result))
            }
    
    # 手动指定方法
    if method in optimizer.methods:
        serialized = optimizer.methods[method](result)
        size = len(serialized.encode('utf-8')) if isinstance(serialized, str) else len(serialized)
        
        return {
            "data_string": serialized,
            "method": method,
            "size": size,
            "compression_ratio": size / len(json.dumps(result))
        }
    
    # 默认回退到紧凑JSON
    json_str = json.dumps(result, ensure_ascii=False, separators=(',', ':'))
    return {
        "data_string": json_str,
        "method": "json_compact",
        "size": len(json_str.encode('utf-8')),
        "compression_ratio": 1.0
    }

# 推荐的优化方案
def convert_result_to_string_recommended(result):
    """推荐的优化方案：JSON + Gzip"""
    import gzip
    import json
    import base64
    
    # 1. 紧凑JSON序列化
    json_str = json.dumps(result, ensure_ascii=False, separators=(',', ':'))
    
    # 2. Gzip压缩
    compressed = gzip.compress(json_str.encode('utf-8'))
    
    # 3. Base64编码（便于存储和传输）
    encoded = base64.b64encode(compressed).decode('ascii')
    
    original_size = len(json_str.encode('utf-8'))
    compressed_size = len(compressed)
    encoded_size = len(encoded)
    
    return {
        "data_string": encoded,
        "method": "json_gzip_b64",
        "original_size": original_size,
        "compressed_size": compressed_size,
        "encoded_size": encoded_size,
        "compression_ratio": compressed_size / original_size,
        "space_saving": (1 - compressed_size / original_size) * 100
    }

def decompress_result(compressed_data, method):
    """解压缩数据"""
    import gzip
    import base64
    import json
    
    if method == "json_gzip_b64":
        # Base64解码 -> Gzip解压 -> JSON解析
        compressed = base64.b64decode(compressed_data.encode('ascii'))
        json_str = gzip.decompress(compressed).decode('utf-8')
        return json.loads(json_str)
    
    # 其他方法的解压逻辑...
    return None

if __name__ == "__main__":
    # 测试示例
    test_data = {
        "policies": [
            {
                "commodityName": "众安百万医疗险",
                "totalPremium": "1200.00",
                "premiumPayment": "100.00",
                "orderDate": "2023-01-15",
                "holderName": "张三",
                "policyName": "张三",
                "policyNo": "ZA202301150001",
                "isFree": False,
                "downGradeFlag": "Y",
                "operationsTypeName": "医疗险"
            } for _ in range(100)  # 模拟100个保单
        ],
        "metadata": {
            "total_count": 100,
            "free_count": 20,
            "non_free_count": 80,
            "timestamp": "2024-01-15T10:30:00Z"
        }
    }
    
    print("🚀 序列化方法性能对比测试")
    print("=" * 80)
    
    optimizer = SerializationOptimizer()
    results = optimizer.benchmark_serialization(test_data)
    
    print("\n📈 推荐方案:")
    best_size = optimizer.get_best_method(results, 'size')
    best_speed = optimizer.get_best_method(results, 'speed')
    best_balanced = optimizer.get_best_method(results, 'balanced')
    
    if best_size:
        print(f"🏆 最小空间: {best_size[0]} ({best_size[1]['size']:,} bytes)")
    if best_speed:
        print(f"⚡ 最快速度: {best_speed[0]} ({best_speed[1]['time_ms']:.2f}ms)")
    if best_balanced:
        print(f"⚖️  综合最佳: {best_balanced[0]}")
