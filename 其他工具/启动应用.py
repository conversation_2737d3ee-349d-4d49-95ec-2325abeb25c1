#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据处理工具启动脚本
"""

import os
import sys

def main():
    print("=" * 60)
    print("           Excel数据处理工具")
    print("=" * 60)
    print()
    print("功能特点：")
    print("✓ 支持4种数据处理方式")
    print("✓ 执行算式：数学运算处理")
    print("✓ 按字段取值：JSON数据提取")
    print("✓ AI写公式：智能生成Excel公式")
    print("✓ AI自定义任务：文本智能处理")
    print()
    print("使用提示：")
    print("1. 选择Excel文件并读取")
    print("2. 配置需要处理的列")
    print("3. 使用'试运行'验证配置")
    print("4. 执行完整处理")
    print()
    print("正在启动应用...")
    print("=" * 60)
    
    # 设置环境变量以消除Tk警告
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    
    # 导入并启动应用
    try:
        from excel_processor_app import main as app_main
        app_main()
    except ImportError:
        print("错误：找不到excel_processor_app.py文件")
        print("请确保在正确的目录中运行此脚本")
    except Exception as e:
        print(f"启动失败：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
