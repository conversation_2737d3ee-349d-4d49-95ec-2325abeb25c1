import sys
import traceback

try:
    print("开始执行测试...")
    
    import re
    
    def simple_freemarker_renderer(template, data):
        """简化版FreeMarker渲染器，只处理简单的if条件"""
        print(f"模板: {template}")
        print(f"数据: {data}")
        
        result = template
        
        # 查找并处理所有<#if ...>...</#if>块
        if_pattern = r"<#if\s+(.*?)>(.*?)</#if>"
        matches = re.findall(if_pattern, template, re.DOTALL)
        
        print(f"找到{len(matches)}个if块")
        
        for condition, content in matches:
            condition = condition.strip()
            print(f"处理条件: '{condition}'")
            
            # 计算条件结果
            condition_result = evaluate_condition(condition, data)
            print(f"条件结果: {condition_result}")
            
            # 根据条件结果替换内容
            if_block = f"<#if {condition}>{content}</#if>"
            if condition_result:
                result = result.replace(if_block, content)
            else:
                result = result.replace(if_block, "")
        
        return result
    
    def evaluate_condition(condition, data):
        """简单的条件评估函数"""
        print(f"评估条件: {condition}")
        
        # 处理字面量true和false
        if condition == "true":
            return True
        if condition == "false":
            return False
        
        # 处理变量名
        if condition in data:
            value = data[condition]
            print(f"变量值: {value}, 类型: {type(value)}")
            return bool(value)
        
        # 处理== 比较
        if " == " in condition:
            parts = condition.split(" == ")
            left = parts[0].strip()
            right = parts[1].strip()
            
            left_value = get_value(left, data)
            
            # 处理右侧字面量
            if right == "true":
                right_value = True
            elif right == "false":
                right_value = False
            else:
                right_value = get_value(right, data)
                
            print(f"比较: {left_value} == {right_value}")
            return left_value == right_value
        
        # 默认情况
        return False
    
    def get_value(expr, data):
        """获取变量值"""
        if expr == "true":
            return True
        if expr == "false":
            return False
        if expr in data:
            return data[expr]
        return None
    
    # 测试布尔字面量
    print("\n--- 测试布尔字面量 ---")
    template1 = "<#if true>条件为真</#if>"
    result1 = simple_freemarker_renderer(template1, {})
    print(f"结果1: '{result1}'")
    
    template2 = "<#if false>条件为假</#if>"
    result2 = simple_freemarker_renderer(template2, {})
    print(f"结果2: '{result2}'")
    
    # 测试布尔变量
    print("\n--- 测试布尔变量 ---")
    template3 = "<#if flag>标志为真</#if>"
    result3_1 = simple_freemarker_renderer(template3, {"flag": True})
    print(f"结果3.1: '{result3_1}'")
    
    result3_2 = simple_freemarker_renderer(template3, {"flag": False})
    print(f"结果3.2: '{result3_2}'")
    
    # 测试布尔相等
    print("\n--- 测试布尔相等 ---")
    template4 = "<#if flag == true>标志等于true</#if>"
    result4_1 = simple_freemarker_renderer(template4, {"flag": True})
    print(f"结果4.1: '{result4_1}'")
    
    result4_2 = simple_freemarker_renderer(template4, {"flag": False})
    print(f"结果4.2: '{result4_2}'")
    
    print("测试完成！")
    
except Exception as e:
    print(f"ERROR: {type(e).__name__}: {e}")
    traceback.print_exc()
    sys.stderr.flush()
    sys.stdout.flush() 