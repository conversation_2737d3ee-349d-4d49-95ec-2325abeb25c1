def main(date='', msg=''):
    """
    主函数，解析消息并返回包含Redis键信息和解析结果的字典

    参数:
        date (str): 日期字符串
        msg (str): 输入的消息字符串

    返回:
        dict: 包含Redis键信息和解析结果的字典
    """
    # 基础Redis键信息
    result = {
        "key": "JYMQ:dailyreport:" + date,
        "summaryKey": "JYMQ:dailySummary:" + date,
        "expireTime": "86400",
        "dateString": date[-5:].replace("-", ""),
        "oper": "",
        "name": "",
        "content": ""
    }

    # 如果没有提供消息，直接返回基础信息
    if not msg:
        return result

    # 预处理：如果消息中包含"不用填:"，则去掉该标记及之后的所有内容
    no_fill_pos = msg.find("不用填:")
    if no_fill_pos != -1:
        msg = msg[:no_fill_pos].strip()

    # 查找操作字段
    oper_start = msg.find("操作:")
    if oper_start != -1:
        oper_start = oper_start + 3  # 跳过"操作:"
        oper_end = msg.find(" ", oper_start)
        if oper_end == -1:
            oper_end = msg.find("姓名:", oper_start)
        if oper_end == -1:
            oper_end = len(msg)
        result.update({"oper": msg[oper_start:oper_end].strip()})

    # 查找姓名字段
    name_start = msg.find("姓名:")
    if name_start != -1:
        name_start = name_start + 3  # 跳过"姓名:"
        name_end = msg.find(" ", name_start)
        if name_end == -1:
            name_end = msg.find("内容:", name_start)
        if name_end == -1:
            name_end = len(msg)
        result.update({"name": msg[name_start:name_end].strip()})

    # 查找内容字段
    content_start = msg.find("内容:")
    if content_start != -1:
        content_start = content_start + 3  # 跳过"内容:"
        result.update({"content": msg[content_start:].strip()})

    return result


# 测试示例
if __name__ == "__main__":
    test_msg = "AI 外呼日报 Agent： 操作: input 姓名: 王旭飞 内容: 1、sql写底表--户均分布，并配置数据看板 2、在贷-版本2：语音信箱两轮挂机和反欺诈开场白上线，测试通过"
    test_date = "2024-01-15"

    # 测试完整功能
    result = main(date=test_date, msg=test_msg)
    print("解析结果:")
    print(f"key: {result['key']}")
    print(f"summaryKey: {result['summaryKey']}")
    print(f"expireTime: {result['expireTime']}")
    print(f"dateString: {result['dateString']}")
    print(f"oper: {result['oper']}")
    print(f"name: {result['name']}")
    print(f"content: {result['content']}")

    print("\n测试缺失字段的情况:")
    # 测试只有操作的情况
    test_msg_partial = "操作: update"
    result_partial = main(date=test_date, msg=test_msg_partial)
    print(f"只有操作时 - oper: '{result_partial['oper']}', name: '{result_partial['name']}', content: '{result_partial['content']}'")

    # 测试空消息的情况
    result_empty = main(date=test_date, msg="")
    print(f"空消息时 - oper: '{result_empty['oper']}', name: '{result_empty['name']}', content: '{result_empty['content']}'")

    print("\n测试'不用填:'功能:")
    # 测试包含"不用填:"的情况
    test_msg_with_no_fill = "操作: input 姓名: 张三 内容: 完成任务A 不用填: 这部分内容会被忽略"
    result_no_fill = main(date=test_date, msg=test_msg_with_no_fill)
    print(f"包含'不用填:'时 - oper: '{result_no_fill['oper']}', name: '{result_no_fill['name']}', content: '{result_no_fill['content']}'")

    # 测试"不用填:"在中间的情况
    test_msg_no_fill_middle = "操作: update 不用填: 后面的内容 姓名: 李四"
    result_no_fill_middle = main(date=test_date, msg=test_msg_no_fill_middle)
    print(f"'不用填:'在中间时 - oper: '{result_no_fill_middle['oper']}', name: '{result_no_fill_middle['name']}', content: '{result_no_fill_middle['content']}'")
