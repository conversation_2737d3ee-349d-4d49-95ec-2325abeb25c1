# AI监工2.0技术实现方案

## 一、系统架构设计

### 1.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据采集层    │    │   异常检测层    │    │   结果处理层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • IVR实时数据   │───▶│ • 实时监控引擎  │───▶│ • 企微触达      │
│ • 触达回传数据  │    │ • 事后分析引擎  │    │ • 数据埋点      │
│ • 会话完整数据  │    │ • LLM分析服务   │    │ • 结果展示      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 核心模块设计

#### 1.2.1 实时监控模块（沿用现有）
- **意图检测服务**: 基于现有LLM+RAG架构
- **异常识别引擎**: 规则引擎+机器学习模型
- **实时触达服务**: 企微API集成

#### 1.2.2 事后分析模块（新增）
- **数据预处理服务**: 会话数据清洗与结构化
- **多维异常检测**: 并行检测多种异常类型
- **智能分析引擎**: 基于大模型的综合分析

## 二、异常检测算法设计

### 2.1 系统异常检测

#### 2.1.1 响应耗时检测
```python
def detect_response_timeout(session_data):
    """检测响应超时异常"""
    for turn in session_data.turns:
        if turn.response_time >= 3000:  # 3秒阈值
            return {
                "type": "response_timeout",
                "severity": "high" if turn.response_time >= 5000 else "medium",
                "details": f"响应耗时{turn.response_time}ms，超过阈值3000ms"
            }
    return None
```

#### 2.1.2 系统静默检测
```python
def detect_system_silence(session_data):
    """检测系统静默异常"""
    silence_periods = []
    for i in range(len(session_data.turns) - 1):
        gap = session_data.turns[i+1].timestamp - session_data.turns[i].timestamp
        if gap >= 5000:  # 5秒静默
            silence_periods.append({
                "duration": gap,
                "start_turn": i,
                "end_turn": i+1
            })
    
    if silence_periods:
        return {
            "type": "system_silence", 
            "count": len(silence_periods),
            "max_duration": max(p["duration"] for p in silence_periods)
        }
    return None
```

### 2.2 业务异常检测

#### 2.2.1 轮次异常检测
```python
def detect_insufficient_turns(session_data):
    """检测轮次过少异常"""
    if (session_data.end_reason == "system_hangup" and 
        session_data.turn_count <= 4):
        return {
            "type": "insufficient_turns",
            "turn_count": session_data.turn_count,
            "end_reason": session_data.end_reason,
            "suggestion": "建议检查话术流程设计，增加用户互动环节"
        }
    return None
```

#### 2.2.2 异常挂断检测
```python
def detect_abnormal_hangup(session_data):
    """检测异常挂断"""
    if (session_data.end_reason == "system_hangup" and 
        not session_data.hit_ending_script):
        
        # 分析是否命中兜底话术
        last_response = session_data.turns[-1].response
        if is_fallback_response(last_response):
            return {
                "type": "abnormal_hangup",
                "subtype": "fallback_triggered",
                "last_response": last_response,
                "suggestion": "系统触发兜底话术，建议检查意图识别准确性"
            }
        else:
            return {
                "type": "abnormal_hangup", 
                "subtype": "unexpected_end",
                "suggestion": "未按预期流程结束，建议检查话术逻辑"
            }
    return None
```

### 2.3 话术质量检测

#### 2.3.1 话术匹配度检测
```python
async def detect_script_mismatch(session_data):
    """检测话术匹配异常"""
    mismatches = []
    
    for turn in session_data.turns:
        if turn.user_input and turn.system_response:
            # 使用LLM评估话术匹配度
            prompt = f"""
            用户输入: {turn.user_input}
            系统回复: {turn.system_response}
            
            请评估系统回复是否与用户输入匹配，返回评分(1-10)和原因。
            """
            
            result = await llm_service.evaluate(prompt)
            if result.score <= 6:  # 匹配度阈值
                mismatches.append({
                    "turn_id": turn.id,
                    "score": result.score,
                    "reason": result.reason
                })
    
    if mismatches:
        return {
            "type": "script_mismatch",
            "mismatch_count": len(mismatches),
            "avg_score": sum(m["score"] for m in mismatches) / len(mismatches),
            "details": mismatches
        }
    return None
```

#### 2.3.2 话术优化检测
```python
async def detect_script_optimization(session_data):
    """检测话术优化机会"""
    optimization_suggestions = []
    
    for turn in session_data.turns:
        if turn.selected_script and turn.candidate_scripts:
            # 使用更优模型重新评估top3话术
            top3_scripts = turn.candidate_scripts[:3]
            
            prompt = f"""
            用户输入: {turn.user_input}
            候选话术: {top3_scripts}
            实际选择: {turn.selected_script}
            
            请评估实际选择是否在最优的top3中，并给出优化建议。
            """
            
            result = await advanced_llm_service.evaluate(prompt)
            if not result.is_optimal:
                optimization_suggestions.append({
                    "turn_id": turn.id,
                    "current_script": turn.selected_script,
                    "suggested_script": result.better_option,
                    "improvement_reason": result.reason
                })
    
    if optimization_suggestions:
        return {
            "type": "script_optimization",
            "optimization_count": len(optimization_suggestions),
            "suggestions": optimization_suggestions
        }
    return None
```

## 三、数据流转设计

### 3.1 数据接口标准

#### 3.1.1 实时数据接口
```json
{
    "session_id": "string",
    "turn_id": "string", 
    "timestamp": "datetime",
    "event_type": "user_input|system_response|system_error",
    "data": {
        "content": "string",
        "intent": "string",
        "response_time": "number",
        "error_code": "string"
    }
}
```

#### 3.1.2 会话结束数据接口
```json
{
    "session_id": "string",
    "start_time": "datetime",
    "end_time": "datetime", 
    "turn_count": "number",
    "end_reason": "user_hangup|system_hangup|timeout",
    "hit_ending_script": "boolean",
    "turns": [
        {
            "turn_id": "string",
            "user_input": "string",
            "system_response": "string", 
            "intent": "string",
            "selected_script": "string",
            "candidate_scripts": ["string"],
            "response_time": "number"
        }
    ]
}
```

### 3.2 异步处理机制

#### 3.2.1 消息队列设计
```python
# 使用Redis Stream实现消息队列
class MonitoringQueue:
    def __init__(self):
        self.redis_client = redis.Redis()
        
    async def publish_realtime_event(self, event_data):
        """发布实时监控事件"""
        await self.redis_client.xadd(
            "realtime_monitoring_stream",
            event_data
        )
    
    async def publish_session_end_event(self, session_data):
        """发布会话结束事件"""
        await self.redis_client.xadd(
            "session_analysis_stream", 
            session_data
        )
```

#### 3.2.2 异常处理与重试
```python
class ExceptionDetector:
    def __init__(self):
        self.max_retries = 3
        self.retry_delay = 1
        
    async def detect_with_retry(self, detection_func, data):
        """带重试的异常检测"""
        for attempt in range(self.max_retries):
            try:
                return await detection_func(data)
            except Exception as e:
                if attempt == self.max_retries - 1:
                    # 记录失败日志
                    logger.error(f"Detection failed after {self.max_retries} attempts: {e}")
                    return None
                await asyncio.sleep(self.retry_delay * (2 ** attempt))
```

## 四、触达与展示设计

### 4.1 企微触达优化

#### 4.1.1 智能触达策略
```python
class SmartNotificationService:
    def __init__(self):
        self.severity_thresholds = {
            "high": 0,      # 立即触达
            "medium": 5,    # 5分钟内批量触达
            "low": 30       # 30分钟内批量触达
        }
    
    async def send_notification(self, exceptions):
        """智能触达策略"""
        high_priority = [e for e in exceptions if e.severity == "high"]
        medium_priority = [e for e in exceptions if e.severity == "medium"] 
        low_priority = [e for e in exceptions if e.severity == "low"]
        
        # 高优先级立即发送
        for exception in high_priority:
            await self.send_immediate_alert(exception)
            
        # 中低优先级批量发送
        if medium_priority:
            await self.schedule_batch_notification(medium_priority, delay=5)
        if low_priority:
            await self.schedule_batch_notification(low_priority, delay=30)
```

### 4.2 结果展示设计

#### 4.2.1 异常分类展示
```javascript
// 前端异常展示组件
const ExceptionDashboard = () => {
    const [exceptions, setExceptions] = useState([]);
    const [activeTab, setActiveTab] = useState('system');
    
    const exceptionTabs = [
        { key: 'system', label: '系统异常', icon: 'warning' },
        { key: 'business', label: '业务异常', icon: 'exclamation' },
        { key: 'quality', label: '话术质量', icon: 'star' }
    ];
    
    return (
        <div className="exception-dashboard">
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
                {exceptionTabs.map(tab => (
                    <TabPane key={tab.key} tab={
                        <span>
                            <Icon type={tab.icon} />
                            {tab.label}
                        </span>
                    }>
                        <ExceptionList 
                            type={tab.key} 
                            exceptions={exceptions.filter(e => e.type === tab.key)}
                        />
                    </TabPane>
                ))}
            </Tabs>
        </div>
    );
};
```

## 五、部署与运维

### 5.1 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  monitoring-service:
    image: ai-monitoring:2.0
    environment:
      - REDIS_URL=redis://redis:6379
      - LLM_API_URL=http://llm-service:8080
    depends_on:
      - redis
      - llm-service
      
  redis:
    image: redis:7-alpine
    
  llm-service:
    image: llm-service:latest
```

### 5.2 监控告警
```python
# 系统健康监控
class HealthMonitor:
    def __init__(self):
        self.metrics = {
            "detection_latency": [],
            "error_rate": 0,
            "queue_depth": 0
        }
    
    async def check_system_health(self):
        """系统健康检查"""
        health_status = {
            "status": "healthy",
            "checks": {
                "redis_connection": await self.check_redis(),
                "llm_service": await self.check_llm_service(),
                "queue_status": await self.check_queue_status()
            }
        }
        
        if any(not check for check in health_status["checks"].values()):
            health_status["status"] = "unhealthy"
            await self.send_alert(health_status)
            
        return health_status
```

## 六、性能优化建议

### 6.1 缓存策略
- LLM结果缓存：相似输入复用结果
- 话术库缓存：热点话术内存缓存
- 用户画像缓存：减少重复查询

### 6.2 并发处理
- 异常检测并行化：多种检测算法并行执行
- 批量处理优化：合并相似请求减少LLM调用
- 连接池管理：复用数据库和API连接

### 6.3 资源管理
- 动态扩缩容：基于队列深度自动扩容
- 限流保护：防止突发流量冲击
- 降级策略：核心功能优先保障
