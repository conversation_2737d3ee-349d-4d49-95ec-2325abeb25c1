<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI监工2.0系统流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.0/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        .description {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        .mermaid {
            text-align: center;
            margin: 30px 0;
        }

        .legend {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 30px;
        }
        .legend-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
        }
        .legend-item h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .legend-item ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .legend-item li {
            margin: 5px 0;
            color: #34495e;
        }
        .highlight {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .new-feature {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>AI监工2.0系统流程图</h1>

        <div class="description">
            <h3>系统概述</h3>
            <p>AI监工2.0在现有监工链路基础上，新增多维度异常监控能力，实现<strong>预提醒监工</strong>与<strong>催收多维监工</strong>双重保障机制。系统能够检测9大类异常情况，提供智能化的异常分析和优化建议。</p>
        </div>

        <!-- 预提醒监工 -->
        <div style="margin: 30px 0;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">预提醒监工（准实时链路）</h2>
            <div class="mermaid">
                    flowchart TD
                        A1[数据入口] --> B1[意图结果处理]
                        B1 --> C1[LLM: 意图初检]
                        B1 --> C2[RAG: 意图语料库召回]

                        C1 --> D1[检查结果处理]
                        C2 --> D1

                        D1 --> E1[LLM: 复检+错误原因分析+优化意图建议]

                        E1 --> F1{检测到异常?}
                        F1 -->|是| G1[企微触达]
                        F1 -->|否| H1[无异常结束]

                        G1 --> I1[触达系统打标]
                        I1 --> J1[意图标注页面展示]
                        J1 -->|跟踪修复进度| J1

                        %% 样式定义
                        style A1 fill:#ff9999,stroke:#333,stroke-width:3px
                        style B1 fill:#bbf,stroke:#333,stroke-width:2px
                        style C1 fill:#fbb,stroke:#333,stroke-width:2px
                        style C2 fill:#fbb,stroke:#333,stroke-width:2px
                        style D1 fill:#bfb,stroke:#333,stroke-width:2px
                        style E1 fill:#fbf,stroke:#333,stroke-width:2px
                        style F1 fill:#fff3cd,stroke:#ffc107,stroke-width:2px
                        style G1 fill:#bff,stroke:#333,stroke-width:2px
                        style H1 fill:#e8f5e8,stroke:#333,stroke-width:2px
                        style I1 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style J1 fill:#f0f8ff,stroke:#333,stroke-width:2px
                </div>
            </div>

        <!-- 催收多维监工 -->
        <div style="margin: 30px 0;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">催收多维监工（准实时链路）</h2>
            <div class="mermaid">
                    flowchart TD
                        A2[会话数据入口] --> B2[会话数据完整性检查]
                        B2 --> C3[多维度异常分析引擎]

                        C3 --> D2[系统异常检测模块]
                        C3 --> D3[业务异常检测模块]
                        C3 --> D4[话术质量检测模块]

                        %% 系统异常检测详细流程
                        D2 --> D2a[响应耗时<br/>≥3秒]
                        D2 --> D2c[系统报错<br/>异常日志]

                        %% 业务异常检测详细流程
                        D3 --> D3a[轮次过少<br/>≤4轮]
                        D3 --> D3b[异常挂断<br/>未命中结束语]
                        D3 --> D3c[通时过长<br/>≥5分钟]

                        %% 话术质量检测详细流程
                        D4 --> D4a[话术匹配<br/>LLM评估]
                        D4 --> D4b[意图分类<br/>准确性检测]
                        D4 --> D4c[话术优化<br/>更优选择]
                        D4 --> D4d[话术评分<br/>质量阈值]

                        %% 异常汇总与智能分析
                        D2a --> E2[异常结果汇总中心]
                        D2c --> E2
                        D3a --> E2
                        D3b --> E2
                        D3c --> E2
                        D4a --> E2
                        D4b --> E2
                        D4c --> E2
                        D4d --> E2

                        E2 --> F2[LLM: 综合分析+优化建议生成]

                        F2 --> G2{检测到异常?}
                        G2 -->|是| H2[企微触达]
                        G2 -->|否| I2[无异常结束]

                        H2 --> J2[触达系统打标]
                        J2 --> K2[AI标注页面展示]
                        K2 -->|跟踪修复进度| K2

                        %% 样式定义
                        style A2 fill:#ff9999,stroke:#333,stroke-width:3px
                        style B2 fill:#bbf,stroke:#333,stroke-width:2px
                        style C3 fill:#f3e5f5,stroke:#333,stroke-width:2px
                        style D2 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style D3 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style D4 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style E2 fill:#e8f5e8,stroke:#333,stroke-width:2px
                        style F2 fill:#fff9c4,stroke:#333,stroke-width:2px
                        style G2 fill:#fff3cd,stroke:#ffc107,stroke-width:2px
                        style H2 fill:#bff,stroke:#333,stroke-width:2px
                        style I2 fill:#e8f5e8,stroke:#333,stroke-width:2px
                        style J2 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                        style K2 fill:#f0f8ff,stroke:#333,stroke-width:2px
                </div>
        </div>

        <!-- AI监工2.0系统架构图 -->
        <div style="margin: 50px 0;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">AI监工2.0系统架构图</h2>
            <div class="description">
                <h3>架构说明</h3>
                <p>完整展现从IVR调用到AI监工异步处理的全流程，包含工程检查、LLM处理、RAG召回和告警机制的系统架构。</p>
            </div>

            <div class="svg-container">
                <svg width="1200" height="1600" viewBox="0 0 1200 1600" xmlns="http://www.w3.org/2000/svg">
                    <!-- 定义箭头样式 -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#6c757d" />
                        </marker>

                        <!-- 阴影效果 -->
                        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                            <feDropShadow dx="2" dy="3" stdDeviation="3" flood-opacity="0.15" />
                        </filter>

                        <!-- 渐变定义 -->
                        <linearGradient id="ivrGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e1f5fe; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #b3e5fc; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="entryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #f3e5f5; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #e1bee7; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="skillGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e8f5e8; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #c8e6c9; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="supervisorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #fff3e0; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #ffe0b2; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="checkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #ffebee; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #ffcdd2; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="llmGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e3f2fd; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #bbdefb; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="ragGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #f1f8e9; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #dcedc8; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="alertGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #fce4ec; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #f8bbd9; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="trackGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #f3e5f5; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #e1bee7; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="memoryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #f9fbe7; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #f0f4c3; stop-opacity: 0.8" />
                        </linearGradient>

                        <linearGradient id="endGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e8f5e8; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #c8e6c9; stop-opacity: 0.8" />
                        </linearGradient>
                    </defs>

                    <!-- 催收2.0流程虚线框 -->
                    <rect x="470" y="40" width="260" height="400" rx="15" fill="none" stroke="#6c757d" stroke-width="2" stroke-dasharray="10,5" />
                    <text x="600" y="30" font-size="16" text-anchor="middle" font-weight="bold" fill="#6c757d">催收2.0流程</text>

                    <!-- 1. IVR单轮话术调用 -->
                    <rect x="500" y="50" width="200" height="60" rx="30" fill="url(#ivrGradient)" stroke="#01579b" stroke-width="2" filter="url(#shadow)" />
                    <text x="600" y="85" font-size="16" text-anchor="middle" font-weight="bold">IVR单轮话术调用</text>

                    <!-- 2. 催收入口 -->
                    <rect x="500" y="150" width="200" height="80" rx="8" fill="url(#entryGradient)" stroke="#4a148c" stroke-width="2" filter="url(#shadow)" />
                    <text x="600" y="180" font-size="16" text-anchor="middle" font-weight="bold">催收入口</text>
                    <text x="600" y="205" font-size="14" text-anchor="middle">记录请求时间</text>

                    <!-- 3. 催收2.0技能 -->
                    <rect x="500" y="270" width="200" height="60" rx="8" fill="url(#skillGradient)" stroke="#1b5e20" stroke-width="2" filter="url(#shadow)" />
                    <text x="600" y="305" font-size="16" text-anchor="middle" font-weight="bold">催收2.0技能</text>

                    <!-- 4. 返回IVR -->
                    <rect x="500" y="370" width="200" height="60" rx="8" fill="url(#ivrGradient)" stroke="#01579b" stroke-width="2" filter="url(#shadow)" />
                    <text x="600" y="405" font-size="16" text-anchor="middle" font-weight="bold">返回IVR</text>

                    <!-- 5. 异步准实时监工 -->
                    <rect x="800" y="270" width="180" height="60" rx="8" fill="url(#entryGradient)" stroke="#4a148c" stroke-width="2" filter="url(#shadow)" />
                    <text x="890" y="305" font-size="14" text-anchor="middle" font-weight="bold">异步准实时监工</text>

                    <!-- 连接线：IVR到催收入口 -->
                    <line x1="600" y1="110" x2="600" y2="150" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 连接线：催收入口到催收技能 -->
                    <line x1="600" y1="230" x2="600" y2="270" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 连接线：催收技能到返回IVR -->
                    <line x1="600" y1="330" x2="600" y2="370" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 连接线：催收技能到异步监工 -->
                    <path d="M 700,300 L 800,300" fill="none" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 6. AI监工核心区域 -->
                    <rect x="50" y="320" width="1140" height="1200" rx="15" fill="none" stroke="#495057" stroke-width="2" stroke-dasharray="10,5" />
                    <text x="600" y="1540" font-size="18" text-anchor="middle" font-weight="bold" fill="#495057">AI监工2.0核心系统</text>

                    <!-- 7. AI监工 -->
                    <rect x="800" y="350" width="180" height="60" rx="8" fill="url(#supervisorGradient)" stroke="#e65100" stroke-width="2" filter="url(#shadow)" />
                    <text x="890" y="385" font-size="16" text-anchor="middle" font-weight="bold">AI监工</text>

                    <!-- 8. 获取会话记忆 -->
                    <rect x="800" y="450" width="180" height="60" rx="8" fill="url(#memoryGradient)" stroke="#827717" stroke-width="2" filter="url(#shadow)" />
                    <text x="890" y="485" font-size="16" text-anchor="middle" font-weight="bold">获取会话记忆</text>

                    <!-- 连接线：异步监工到AI监工 -->
                    <line x1="890" y1="330" x2="890" y2="350" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 连接线：AI监工到会话记忆 -->
                    <line x1="890" y1="410" x2="890" y2="450" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 9. 工程检查模块 -->
                    <rect x="100" y="550" width="800" height="300" rx="10" fill="url(#checkGradient)" stroke="#b71c1c" stroke-width="2" filter="url(#shadow)" />
                    <text x="500" y="580" font-size="18" text-anchor="middle" font-weight="bold">工程检查模块</text>

                    <!-- 工程检查子项 -->
                    <rect x="130" y="600" width="160" height="80" rx="8" fill="#fff" stroke="#b71c1c" stroke-width="1.5" />
                    <text x="210" y="630" font-size="14" text-anchor="middle" font-weight="bold">系统异常检查</text>
                    <text x="210" y="650" font-size="12" text-anchor="middle">未返回有效</text>
                    <text x="210" y="665" font-size="12" text-anchor="middle">意图ID</text>

                    <rect x="310" y="600" width="160" height="80" rx="8" fill="#fff" stroke="#b71c1c" stroke-width="1.5" />
                    <text x="390" y="630" font-size="14" text-anchor="middle" font-weight="bold">响应超时检查</text>
                    <text x="390" y="650" font-size="12" text-anchor="middle">超过3秒阈值</text>

                    <rect x="490" y="600" width="160" height="80" rx="8" fill="#fff" stroke="#b71c1c" stroke-width="1.5" />
                    <text x="570" y="630" font-size="14" text-anchor="middle" font-weight="bold">轮次检查</text>
                    <text x="570" y="650" font-size="12" text-anchor="middle">少于4轮要求</text>

                    <rect x="670" y="600" width="160" height="80" rx="8" fill="#fff" stroke="#b71c1c" stroke-width="1.5" />
                    <text x="750" y="630" font-size="14" text-anchor="middle" font-weight="bold">通时检查</text>
                    <text x="750" y="650" font-size="12" text-anchor="middle">超过5分钟阈值</text>

                    <!-- 检查出异常 - 居中放大 -->
                    <rect x="350" y="720" width="200" height="80" rx="8" fill="#ffebee" stroke="#b71c1c" stroke-width="2" />
                    <text x="450" y="770" font-size="18" text-anchor="middle" font-weight="bold">检查出异常</text>

                    <!-- 10. 统一告警系统 -->
                    <rect x="970" y="600" width="200" height="300" rx="10" fill="url(#alertGradient)" stroke="#880e4f" stroke-width="2" filter="url(#shadow)" />
                    <text x="1070" y="630" font-size="16" text-anchor="middle" font-weight="bold">触达企微告警群</text>

                    <!-- 告警类型 -->
                    <rect x="990" y="650" width="160" height="30" rx="5" fill="#fff" stroke="#880e4f" stroke-width="1" />
                    <text x="1070" y="670" font-size="12" text-anchor="middle">工程异常告警</text>

                    <rect x="990" y="690" width="160" height="30" rx="5" fill="#fff" stroke="#880e4f" stroke-width="1" />
                    <text x="1070" y="710" font-size="12" text-anchor="middle">意图大类异常告警</text>

                    <rect x="990" y="730" width="160" height="30" rx="5" fill="#fff" stroke="#880e4f" stroke-width="1" />
                    <text x="1070" y="750" font-size="12" text-anchor="middle">话术选择异常告警</text>

                    <rect x="990" y="770" width="160" height="30" rx="5" fill="#fff" stroke="#880e4f" stroke-width="1" />
                    <text x="1070" y="790" font-size="12" text-anchor="middle">无适合话术告警</text>

                    <rect x="990" y="810" width="160" height="30" rx="5" fill="#fff" stroke="#880e4f" stroke-width="1" />
                    <text x="1070" y="830" font-size="12" text-anchor="middle">推荐话术建议</text>

                    <!-- 15. 触达系统打标 -->
                    <rect x="970" y="950" width="200" height="80" rx="10" fill="url(#trackGradient)" stroke="#6a1b9a" stroke-width="2" filter="url(#shadow)" />
                    <text x="1070" y="980" font-size="14" text-anchor="middle" font-weight="bold">触达系统打标</text>
                    <text x="1070" y="1005" font-size="12" text-anchor="middle">跟踪修复进度</text>

                    <!-- 连接线：触达企微告警群到触达系统打标 -->
                    <line x1="1050" y1="900" x2="1050" y2="950" stroke="#880e4f" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 连接线：获取会话记忆到工程检查 -->
                    <path d="M 890,510 L 890,530 L 500,530 L 500,550" fill="none" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 连接线：各检查项到检查出异常 -->
                    <path d="M 210,680 L 210,700 L 450,700 L 450,720" fill="none" stroke="#d32f2f" stroke-width="2" marker-end="url(#arrowhead)" />
                    <path d="M 390,680 L 390,700 L 450,700" fill="none" stroke="#d32f2f" stroke-width="2" />
                    <path d="M 570,680 L 570,700 L 450,700" fill="none" stroke="#d32f2f" stroke-width="2" />
                    <path d="M 750,680 L 750,700 L 450,700" fill="none" stroke="#d32f2f" stroke-width="2" />

                    <!-- 连接线：检查出异常到告警 -->
                    <path d="M 550,760 L 970,760" fill="none" stroke="#d32f2f" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 11. LLM处理模块 -->
                    <rect x="100" y="900" width="800" height="500" rx="10" fill="url(#llmGradient)" stroke="#0d47a1" stroke-width="2" filter="url(#shadow)" />
                    <text x="500" y="930" font-size="18" text-anchor="middle" font-weight="bold">LLM处理模块</text>

                    <!-- 意图大类正确性判断 -->
                    <rect x="130" y="950" width="740" height="60" rx="8" fill="#fff" stroke="#0d47a1" stroke-width="1.5" />
                    <text x="500" y="985" font-size="16" text-anchor="middle" font-weight="bold">LLM判断意图大类正确性</text>

                    <!-- 并行处理区域 -->
                    <rect x="130" y="1040" width="740" height="120" rx="8" fill="#f8f9fa" stroke="#0d47a1" stroke-width="1.5" />
                    <text x="500" y="1070" font-size="14" text-anchor="middle" font-weight="bold">并行处理</text><!--  -->

                    <rect x="230" y="1090" width="220" height="50" rx="5" fill="#fff" stroke="#0d47a1" stroke-width="1" />
                    <text x="340" y="1120" font-size="14" text-anchor="middle" font-weight="bold">LLM客户意图改写</text>

                    <rect x="560" y="1090" width="220" height="50" rx="5" fill="#fff" stroke="#0d47a1" stroke-width="1" />
                    <text x="670" y="1120" font-size="14" text-anchor="middle" font-weight="bold">切割最新会话</text>

                    <!-- 双路RAG召回 -->
                    <rect x="130" y="1190" width="740" height="100" rx="8" fill="url(#ragGradient)" stroke="#33691e" stroke-width="1.5" />
                    <text x="500" y="1220" font-size="14" text-anchor="middle" font-weight="bold">双路RAG召回</text>

                    <rect x="250" y="1240" width="500" height="40" rx="5" fill="#fff" stroke="#33691e" stroke-width="1" />
                    <text x="500" y="1265" font-size="14" text-anchor="middle" font-weight="bold">召回知识库FAQ话术集</text>

                    <!-- 话术判断 -->
                    <rect x="130" y="1310" width="740" height="60" rx="8" fill="#fff" stroke="#0d47a1" stroke-width="1.5" />
                    <text x="500" y="1345" font-size="14" text-anchor="middle" font-weight="bold">LLM基于FAQ和话术包判断挑选话术的合理性</text>

                    <!-- 连接线：工程检查无异常到LLM -->
                    <path d="M 500,850 L 500,870 L 500,900" fill="none" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)" />
                    <rect x="470" y="860" width="60" height="20" rx="10" fill="white" stroke="#4caf50" stroke-width="1" />
                    <text x="500" y="875" font-size="10" text-anchor="middle" fill="#4caf50">工程检查通过</text>

                    <!-- 连接线：意图正确性判断到告警 -->
                    <path d="M 870,980 L 920,980 L 920,760 L 970,760" fill="none" stroke="#d32f2f" stroke-width="2" marker-end="url(#arrowhead)" />
                    <rect x="880" y="970" width="40" height="20" rx="10" fill="white" stroke="#d32f2f" stroke-width="1" />
                    <text x="900" y="985" font-size="10" text-anchor="middle" fill="#d32f2f">不正确</text>

                    <!-- 连接线：意图正确后到并行处理 -->
                    <line x1="500" y1="1010" x2="500" y2="1040" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)" />
                    <rect x="470" y="1020" width="60" height="20" rx="10" fill="white" stroke="#4caf50" stroke-width="1" />
                    <text x="500" y="1035" font-size="10" text-anchor="middle" fill="#4caf50">意图正确</text>

                    <!-- 连接线：并行处理到RAG召回 -->
                    <line x1="500" y1="1160" x2="500" y2="1190" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 连接线：RAG召回到话术判断 -->
                    <line x1="500" y1="1290" x2="500" y2="1310" stroke="#33691e" stroke-width="2" marker-end="url(#arrowhead)" />

                    <!-- 连接线：话术判断到告警 -->
                    <path d="M 870,1340 L 920,1340 L 920,760 L 970,760" fill="none" stroke="#d32f2f" stroke-width="2" marker-end="url(#arrowhead)" />
                    <rect x="880" y="1330" width="40" height="20" rx="10" fill="white" stroke="#d32f2f" stroke-width="1" />
                    <text x="900" y="1345" font-size="10" text-anchor="middle" fill="#d32f2f">不合适</text>

                    <!-- 14. 正常结束 -->
                    <rect x="400" y="1430" width="200" height="60" rx="30" fill="url(#endGradient)" stroke="#2e7d32" stroke-width="2" filter="url(#shadow)" />
                    <text x="500" y="1465" font-size="16" text-anchor="middle" font-weight="bold">正常结束本轮监工</text>

                    <!-- 连接线：话术判断合适到结束 -->
                    <path d="M 500,1370 L 500,1430" fill="none" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)" />
                    <rect x="470" y="1390" width="60" height="20" rx="10" fill="white" stroke="#4caf50" stroke-width="1" />
                    <text x="500" y="1405" font-size="10" text-anchor="middle" fill="#4caf50">话术合适</text>

                </svg>
            </div>
        </div>

        <div class="legend">
            <div class="legend-item">
                <h3>🔍 系统异常监控</h3>
                <ul>
                    <li><strong>响应超时</strong>: 灵犀响应时间≥3秒</li>
                    <li><strong>系统报错</strong>: 捕获异常日志并分析</li>
                </ul>
            </div>

            <div class="legend-item">
                <h3>📊 业务异常监控</h3>
                <ul>
                    <li><strong>轮次过少</strong>: 系统挂断但对话≤4轮</li>
                    <li><strong>异常挂断</strong>: 未命中结束语的系统挂断</li>
                    <li><strong>通时过长</strong>: 单次通话时长≥5分钟</li>
                </ul>
            </div>

            <div class="legend-item">
                <h3>💬 话术质量监控</h3>
                <ul>
                    <li><strong>话术不匹配</strong>: 回复与用户意图不符</li>
                    <li><strong>意图分类错误</strong>: 一级意图识别错误</li>
                    <li><strong>话术非最优</strong>: 存在更优话术选择</li>
                    <li><strong>话术评分过低</strong>: 质量评分低于阈值</li>
                </ul>
            </div>

            <div class="legend-item highlight">
                <h3>⚡ 预提醒监工链路</h3>
                <ul>
                    <li>沿用现有监工架构</li>
                    <li>优化LLM调用效率</li>
                    <li>增强异常检测精度</li>
                    <li>意图标注页面展示闭环</li>
                </ul>
            </div>

            <div class="legend-item new-feature">
                <h3>🆕 催收多维监工链路</h3>
                <ul>
                    <li>基于会话数据准实时触发</li>
                    <li>多维度并行异常检测</li>
                    <li>智能异常聚合分析</li>
                    <li>AI标注页面展示闭环</li>
                </ul>
            </div>

            <div class="legend-item new-feature">
                <h3>🎯 闭环跟踪机制</h3>
                <ul>
                    <li><strong>预提醒监工</strong>: 意图标注页面展示</li>
                    <li><strong>催收多维监工</strong>: AI标注页面展示</li>
                    <li><strong>状态标注</strong>: 可标注跟进状态</li>
                    <li><strong>形成闭环</strong>: 完整的监控-触达-跟踪流程</li>
                </ul>
            </div>
        </div>

        <!-- 系统架构图说明 -->
        <div class="legend" style="margin-top: 30px;">
            <div class="legend-item" style="border-left-color: #01579b;">
                <h3>🏗️ IVR系统</h3>
                <ul>
                    <li><strong>IVR单轮话术调用</strong>: 发起催收对话</li>
                    <li><strong>返回IVR</strong>: 同步返回响应结果</li>
                    <li><strong>同步流程</strong>: 保证响应时效</li>
                </ul>
            </div>

            <div class="legend-item" style="border-left-color: #4a148c;">
                <h3>📞 催收系统</h3>
                <ul>
                    <li><strong>催收入口</strong>: 记录请求时间戳</li>
                    <li><strong>催收2.0技能</strong>: 核心业务处理</li>
                    <li><strong>异步发起请求</strong>: 触发AI监工</li>
                </ul>
            </div>

            <div class="legend-item" style="border-left-color: #e65100;">
                <h3>🤖 AI监工核心</h3>
                <ul>
                    <li><strong>AI监工</strong>: 主控制器</li>
                    <li><strong>会话记忆</strong>: 对话历史管理</li>
                    <li><strong>异步处理</strong>: 不影响主流程</li>
                </ul>
            </div>

            <div class="legend-item" style="border-left-color: #b71c1c;">
                <h3>🔍 工程检查模块</h3>
                <ul>
                    <li><strong>系统异常</strong>: 未返回有效意图ID</li>
                    <li><strong>响应超时</strong>: 超过3秒阈值</li>
                    <li><strong>轮次过少</strong>: 少于4轮要求</li>
                    <li><strong>通时过长</strong>: 超过5分钟阈值</li>
                </ul>
            </div>

            <div class="legend-item" style="border-left-color: #0d47a1;">
                <h3>🧠 LLM处理模块</h3>
                <ul>
                    <li><strong>意图大类反查</strong>: 根据话术ID查找</li>
                    <li><strong>意图正确性判断</strong>: LLM验证</li>
                    <li><strong>客户意图改写</strong>: 最新意图提取</li>
                    <li><strong>话术合适性判断</strong>: 最终评估</li>
                </ul>
            </div>

            <div class="legend-item" style="border-left-color: #33691e;">
                <h3>📚 RAG召回模块</h3>
                <ul>
                    <li><strong>双路RAG召回</strong>: 多路径检索</li>
                    <li><strong>话术包结合</strong>: 意图大类匹配</li>
                    <li><strong>智能检索</strong>: 提升匹配精度</li>
                </ul>
            </div>

            <div class="legend-item" style="border-left-color: #880e4f;">
                <h3>🚨 告警系统</h3>
                <ul>
                    <li><strong>企微告警群</strong>: 工程异常通知</li>
                    <li><strong>意图大类异常</strong>: 分类错误告警</li>
                    <li><strong>话术选择异常</strong>: 不合适话术告警</li>
                    <li><strong>无适合话术</strong>: 推荐新话术</li>
                </ul>
            </div>

            <div class="legend-item" style="border-left-color: #2e7d32;">
                <h3>✅ 流程结束</h3>
                <ul>
                    <li><strong>所有检查通过</strong>: 无异常发现</li>
                    <li><strong>监工流程完成</strong>: 正常结束本轮</li>
                    <li><strong>质量保证</strong>: 确保话术质量</li>
                </ul>
            </div>
        </div>


        <!-- AI监工闭环管理机制图 -->
        <div style="margin: 50px 0;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">AI监工闭环管理机制</h2>
            <div class="description">
                <h3>闭环管理核心价值</h3>
                <p> | <strong>全程可追溯</strong>：每个环节都有明确的状态标识和时间戳      | <strong>智能化驱动</strong>：AI自动检测和分析，减少人工干预<br>
                      | <strong>分级响应</strong>：根据异常严重程度采用不同处理策略        | <strong>持续改进</strong>：通过复验结果优化检测算法和处理流程</p>
            </div>

            <div class="svg-container">
                <svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
                    <!-- 定义箭头样式 -->
                    <defs>
                        <marker id="arrowhead-loop" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                        </marker>

                        <!-- 阴影效果 -->
                        <filter id="shadow-loop" x="-20%" y="-20%" width="140%" height="140%">
                            <feDropShadow dx="2" dy="3" stdDeviation="3" flood-opacity="0.2" />
                        </filter>

                        <!-- 渐变定义 -->
                        <linearGradient id="businessGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e3f2fd; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #bbdefb; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #f3e5f5; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #e1bee7; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="alertGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #fff3e0; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #ffe0b2; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="trackGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e8f5e9; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #c8e6c9; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="monitorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #fce4ec; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #f8bbd9; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="verifyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e0f2f1; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #b2dfdb; stop-opacity: 0.9" />
                        </linearGradient>
                    </defs>

                    <!-- 背景框 -->
                    <rect x="50" y="50" width="1100" height="700" rx="20" fill="none" stroke="#34495e" stroke-width="2" stroke-dasharray="8,4" />
                    <text x="600" y="40" font-size="16" text-anchor="middle" font-weight="bold" fill="#34495e">AI监工闭环管理机制</text>

                    <!-- 1. 催收外呼 -->
                    <rect x="100" y="150" width="160" height="100" rx="15" fill="url(#businessGradient)" stroke="#1976d2" stroke-width="3" filter="url(#shadow-loop)" />
                    <text x="180" y="185" font-size="16" text-anchor="middle" font-weight="bold" fill="#1976d2">催收外呼</text>
                    <text x="180" y="210" font-size="12" text-anchor="middle" fill="#555">业务触发</text>
                    <text x="180" y="225" font-size="12" text-anchor="middle" fill="#555">产生会话数据</text>

                    <!-- 2. AI监工 -->
                    <rect x="350" y="150" width="160" height="100" rx="15" fill="url(#aiGradient)" stroke="#7b1fa2" stroke-width="3" filter="url(#shadow-loop)" />
                    <text x="430" y="185" font-size="16" text-anchor="middle" font-weight="bold" fill="#7b1fa2">AI监工</text>
                    <text x="430" y="210" font-size="12" text-anchor="middle" fill="#555">智能检测</text>
                    <text x="430" y="225" font-size="12" text-anchor="middle" fill="#555">多维度异常分析</text>

                    <!-- 3. 异常告警 -->
                    <rect x="600" y="150" width="160" height="100" rx="15" fill="url(#alertGradient)" stroke="#f57c00" stroke-width="3" filter="url(#shadow-loop)" />
                    <text x="680" y="185" font-size="16" text-anchor="middle" font-weight="bold" fill="#f57c00">异常告警</text>
                    <text x="680" y="210" font-size="12" text-anchor="middle" fill="#555">分级触达（规划中）</text>
                    <text x="680" y="225" font-size="12" text-anchor="middle" fill="#555">及时通知相关人员</text>

                    <!-- 4. 触达打标 -->
                    <rect x="850" y="150" width="160" height="100" rx="15" fill="url(#trackGradient)" stroke="#388e3c" stroke-width="3" filter="url(#shadow-loop)" />
                    <text x="930" y="185" font-size="16" text-anchor="middle" font-weight="bold" fill="#388e3c">触点打标</text>
                    <text x="930" y="210" font-size="12" text-anchor="middle" fill="#555">埋点落库</text>
                    <text x="930" y="225" font-size="12" text-anchor="middle" fill="#555">建立跟踪记录</text>

                    <!-- 5. 异常跟踪 -->
                    <rect x="850" y="350" width="160" height="100" rx="15" fill="url(#monitorGradient)" stroke="#c2185b" stroke-width="3" filter="url(#shadow-loop)" />
                    <text x="930" y="385" font-size="16" text-anchor="middle" font-weight="bold" fill="#c2185b">异常跟踪</text>
                    <text x="930" y="410" font-size="12" text-anchor="middle" fill="#555">持续监控</text>
                    <text x="930" y="425" font-size="12" text-anchor="middle" fill="#555">确保问题处理进度</text>

                    <!-- 6. 生产复验 -->
                    <rect x="600" y="350" width="160" height="100" rx="15" fill="url(#verifyGradient)" stroke="#00796b" stroke-width="3" filter="url(#shadow-loop)" />
                    <text x="680" y="385" font-size="16" text-anchor="middle" font-weight="bold" fill="#00796b">生产复验</text>
                    <text x="680" y="410" font-size="12" text-anchor="middle" fill="#555">效果验证</text>
                    <text x="680" y="425" font-size="12" text-anchor="middle" fill="#555">形成改进闭环</text>

                    <!-- 连接线 1->2 -->
                    <line x1="260" y1="200" x2="350" y2="200" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead-loop)" />
                    <text x="305" y="190" font-size="11" text-anchor="middle" fill="#2c3e50" font-weight="bold">数据流转</text>

                    <!-- 连接线 2->3 -->
                    <line x1="510" y1="200" x2="600" y2="200" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead-loop)" />
                    <text x="555" y="190" font-size="11" text-anchor="middle" fill="#2c3e50" font-weight="bold">异常检出</text>

                    <!-- 连接线 3->4 -->
                    <line x1="760" y1="200" x2="850" y2="200" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead-loop)" />
                    <text x="805" y="190" font-size="11" text-anchor="middle" fill="#2c3e50" font-weight="bold">触达响应</text>

                    <!-- 连接线 4->5 -->
                    <line x1="930" y1="250" x2="930" y2="350" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead-loop)" />
                    <text x="950" y="300" font-size="11" text-anchor="middle" fill="#2c3e50" font-weight="bold">状态跟踪</text>

                    <!-- 连接线 5->6 -->
                    <line x1="850" y1="400" x2="760" y2="400" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead-loop)" />
                    <text x="805" y="390" font-size="11" text-anchor="middle" fill="#2c3e50" font-weight="bold">处理完成</text>

                    <!-- 连接线 6->2 (闭环) -->
                    <path d="M 600,400 L 430,400 L 430,250" fill="none" stroke="#e74c3c" stroke-width="3" stroke-dasharray="8,4" marker-end="url(#arrowhead-loop)" />
                    <text x="515" y="415" font-size="11" text-anchor="middle" fill="#e74c3c" font-weight="bold">持续改进</text>

                    <!-- 状态指示器 -->
                    <g transform="translate(100, 550)">
                        <rect x="0" y="0" width="1000" height="150" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" />
                        <text x="500" y="25" font-size="14" text-anchor="middle" font-weight="bold" fill="#495057">闭环管理关键指标</text>

                        <!-- 指标1 -->
                        <rect x="50" y="40" width="180" height="80" rx="8" fill="white" stroke="#007bff" stroke-width="2" />
                        <text x="140" y="60" font-size="12" text-anchor="middle" font-weight="bold" fill="#007bff">覆盖率</text>
                        <text x="140" y="80" font-size="20" text-anchor="middle" font-weight="bold" fill="#007bff">100%</text>
                        <text x="140" y="100" font-size="10" text-anchor="middle" fill="#6c757d">全量监控</text>

                        <!-- 指标2 -->
                        <rect x="260" y="40" width="180" height="80" rx="8" fill="white" stroke="#28a745" stroke-width="2" />
                        <text x="350" y="60" font-size="12" text-anchor="middle" font-weight="bold" fill="#28a745">响应时效</text>
                        <text x="350" y="80" font-size="20" text-anchor="middle" font-weight="bold" fill="#28a745">≤1分钟</text>
                        <text x="350" y="100" font-size="10" text-anchor="middle" fill="#6c757d">准实时告警</text>

                        <!-- 指标3 -->
                        <rect x="470" y="40" width="180" height="80" rx="8" fill="white" stroke="#ffc107" stroke-width="2" />
                        <text x="560" y="60" font-size="12" text-anchor="middle" font-weight="bold" fill="#ffc107">处理闭环率(预期)</text>
                        <text x="560" y="80" font-size="20" text-anchor="middle" font-weight="bold" fill="#ffc107">≥90%</text>
                        <text x="560" y="100" font-size="10" text-anchor="middle" fill="#6c757d">跟踪到底</text>

                        <!-- 指标4 -->
                        <rect x="680" y="40" width="180" height="80" rx="8" fill="white" stroke="#dc3545" stroke-width="2" />
                        <text x="770" y="60" font-size="12" text-anchor="middle" font-weight="bold" fill="#dc3545">召回率</text>
                        <text x="770" y="80" font-size="20" text-anchor="middle" font-weight="bold" fill="#dc3545">≥80%</text>
                        <text x="770" y="100" font-size="10" text-anchor="middle" fill="#6c757d">模型迭代</text>
                    </g>

                    <!-- 闭环标识 -->
                    <circle cx="600" cy="300" r="80" fill="none" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,3" opacity="0.6" />
                    <text x="600" y="305" font-size="14" text-anchor="middle" font-weight="bold" fill="#e74c3c">闭环管理</text>
                </svg>
            </div>
        </div>

        <!-- 新增：话术优化闭环流程 -->
        <div style="margin: 50px 0;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">话术优化闭环流程</h2>
            <div class="description">
                <h3>闭环核心思路</h3>
                <p><strong>监工Agent双路线输出</strong>：Bad Case触发异常告警链路，Good Case触发话术优化链路 |
                   <strong>Bad Case链路</strong>：异常告警→AI语音外呼平台打标→异常跟踪→催收Agent生产复验 |
                   <strong>Good Case链路</strong>：VOC话术萃取→AI语音外呼平台专家审核→催收Agent话术上线 |
                   <strong>参与方闭环</strong>：各系统角色明确，操作流程清晰，形成完整业务闭环</p>
            </div>

            <div class="svg-container">
                <svg width="1500" height="1200" viewBox="0 0 1500 1200" xmlns="http://www.w3.org/2000/svg">
                    <!-- 定义箭头样式 -->
                    <defs>
                        <marker id="arrowhead-new" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                        </marker>
                        <marker id="arrowhead-bad" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
                        </marker>
                        <marker id="arrowhead-good" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
                        </marker>

                        <!-- 阴影效果 -->
                        <filter id="shadow-new" x="-20%" y="-20%" width="140%" height="140%">
                            <feDropShadow dx="2" dy="3" stdDeviation="3" flood-opacity="0.2" />
                        </filter>

                        <!-- 渐变定义 -->
                        <linearGradient id="platformGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e3f2fd; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #bbdefb; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="ivrNewGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #f1f8e9; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #dcedc8; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="agentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #fff3e0; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #ffe0b2; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="supervisorNewGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #fce4ec; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #f8bbd9; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="vocGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e8f5e9; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #c8e6c9; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="alertGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #ffebee; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #ffcdd2; stop-opacity: 0.9" />
                        </linearGradient>
                    </defs>

                    <!-- 背景框 -->
                    <rect x="50" y="50" width="1400" height="1100" rx="20" fill="none" stroke="#34495e" stroke-width="2" stroke-dasharray="8,4" />
                    <text x="750" y="40" font-size="18" text-anchor="middle" font-weight="bold" fill="#34495e">话术优化闭环流程</text>

                    <!-- 主流程：上方横向布局 -->
                    <!-- 1. AI语音外呼平台 -->
                    <rect x="100" y="100" width="200" height="100" rx="15" fill="url(#platformGradient)" stroke="#1976d2" stroke-width="3" filter="url(#shadow-new)" />
                    <text x="200" y="130" font-size="16" text-anchor="middle" font-weight="bold" fill="#1976d2">AI语音外呼平台</text>
                    <text x="200" y="150" font-size="12" text-anchor="middle" fill="#555">• 催收外呼任务发起</text>
                    <text x="200" y="165" font-size="12" text-anchor="middle" fill="#555">• 异常跟踪打标</text>
                    <text x="200" y="180" font-size="12" text-anchor="middle" fill="#555">• 业务专家审核界面</text>

                    <!-- 2. IVR -->
                    <rect x="350" y="100" width="180" height="100" rx="15" fill="url(#ivrNewGradient)" stroke="#388e3c" stroke-width="3" filter="url(#shadow-new)" />
                    <text x="440" y="135" font-size="16" text-anchor="middle" font-weight="bold" fill="#388e3c">IVR</text>
                    <text x="440" y="155" font-size="12" text-anchor="middle" fill="#555">• 发起外呼</text>
                    <text x="440" y="170" font-size="12" text-anchor="middle" fill="#555">• 调用催收Agent</text>
                    <text x="440" y="185" font-size="12" text-anchor="middle" fill="#555">• 获取回复话术</text>

                    <!-- 3. 催收Agent -->
                    <rect x="580" y="100" width="180" height="100" rx="15" fill="url(#agentGradient)" stroke="#f57c00" stroke-width="3" filter="url(#shadow-new)" />
                    <text x="670" y="135" font-size="16" text-anchor="middle" font-weight="bold" fill="#f57c00">催收Agent</text>
                    <text x="670" y="155" font-size="12" text-anchor="middle" fill="#555">• 会话内容决策话术</text>
                    <text x="670" y="170" font-size="12" text-anchor="middle" fill="#555">• 话术上线</text>
                    <text x="670" y="185" font-size="12" text-anchor="middle" fill="#555">• 生产复验</text>

                    <!-- 4. 监工Agent -->
                    <rect x="810" y="100" width="200" height="100" rx="15" fill="url(#supervisorNewGradient)" stroke="#c2185b" stroke-width="3" filter="url(#shadow-new)" />
                    <text x="910" y="130" font-size="16" text-anchor="middle" font-weight="bold" fill="#c2185b">监工Agent</text>
                    <text x="910" y="150" font-size="12" text-anchor="middle" fill="#555">• 多维异常检测</text>
                    <text x="910" y="165" font-size="12" text-anchor="middle" fill="#555">• Bad Case / Good Case判断</text>
                    <text x="910" y="180" font-size="12" text-anchor="middle" fill="#555">• 客户意图大类标注</text>

                    <!-- 主流程连接线 -->
                    <line x1="300" y1="150" x2="350" y2="150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead-new)" />
                    <text x="325" y="140" font-size="10" text-anchor="middle" fill="#2c3e50">调用</text>

                    <line x1="530" y1="150" x2="580" y2="150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead-new)" />
                    <text x="555" y="140" font-size="10" text-anchor="middle" fill="#2c3e50">调用</text>

                    <line x1="760" y1="150" x2="810" y2="150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead-new)" />
                    <text x="785" y="140" font-size="10" text-anchor="middle" fill="#2c3e50">异步调用</text>

                    <!-- Bad Case 路线 -->
                    <text x="750" y="280" font-size="16" text-anchor="middle" font-weight="bold" fill="#e74c3c">Bad Case 路线</text>

                    <!-- 企微告警群 -->
                    <rect x="1100" y="300" width="180" height="80" rx="15" fill="url(#alertGradient)" stroke="#e74c3c" stroke-width="3" filter="url(#shadow-new)" />
                    <text x="1190" y="330" font-size="14" text-anchor="middle" font-weight="bold" fill="#e74c3c">企微告警群</text>
                    <text x="1190" y="350" font-size="12" text-anchor="middle" fill="#555">• 异常通知</text>
                    <text x="1190" y="365" font-size="12" text-anchor="middle" fill="#555">• 分级触达</text>

                    <!-- Bad Case 连接线：监工Agent->企微告警群 -->
                    <path d="M 910,200 L 910,250 L 1190,250 L 1190,300" fill="none" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-bad)" />
                    <text x="1050" y="240" font-size="12" text-anchor="middle" fill="#e74c3c" font-weight="bold">Bad Case</text>

                    <!-- Bad Case 连接线：企微告警群->AI语音外呼平台 -->
                    <path d="M 1100,340 L 200,340 L 200,200" fill="none" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-bad)" />
                    <text x="650" y="330" font-size="12" text-anchor="middle" fill="#e74c3c" font-weight="bold">异常跟踪打标</text>

                    <!-- Bad Case 连接线：AI语音外呼平台->催收Agent -->
                    <path d="M 300,150 L 320,150 L 320,260 L 670,260 L 670,200" fill="none" stroke="#e74c3c" stroke-width="3" stroke-dasharray="8,4" marker-end="url(#arrowhead-bad)" />
                    <text x="495" y="250" font-size="12" text-anchor="middle" fill="#e74c3c" font-weight="bold">生产复验</text>

                    <!-- Good Case 路线 -->
                    <text x="750" y="520" font-size="16" text-anchor="middle" font-weight="bold" fill="#27ae60">Good Case 路线</text>

                    <!-- VOC话术萃取Agent -->
                    <rect x="1100" y="540" width="200" height="100" rx="15" fill="url(#vocGradient)" stroke="#2e7d32" stroke-width="3" filter="url(#shadow-new)" />
                    <text x="1200" y="570" font-size="14" text-anchor="middle" font-weight="bold" fill="#2e7d32">VOC话术萃取Agent</text>
                    <text x="1200" y="590" font-size="12" text-anchor="middle" fill="#555">• 每日凌晨批量分析</text>
                    <text x="1200" y="605" font-size="12" text-anchor="middle" fill="#555">• 结合催收阶段、性别标签</text>
                    <text x="1200" y="620" font-size="12" text-anchor="middle" fill="#555">• 萃取新话术</text>

                    <!-- Good Case 连接线：监工Agent->VOC -->
                    <path d="M 910,200 L 910,490 L 1200,490 L 1200,540" fill="none" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead-good)" />
                    <text x="1055" y="480" font-size="12" text-anchor="middle" fill="#27ae60" font-weight="bold">Good Case</text>

                    <!-- Good Case 连接线：VOC->AI语音外呼平台 -->
                    <path d="M 1100,590 L 200,590 L 200,200" fill="none" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead-good)" />
                    <text x="650" y="580" font-size="12" text-anchor="middle" fill="#27ae60" font-weight="bold">新话术推荐 + 专家审核</text>

                    <!-- Good Case 连接线：AI语音外呼平台->催收Agent -->
                    <path d="M 300,150 L 320,150 L 320,450 L 670,450 L 670,200" fill="none" stroke="#27ae60" stroke-width="3" stroke-dasharray="8,4" marker-end="url(#arrowhead-good)" />
                    <text x="505" y="440" font-size="12" text-anchor="middle" fill="#27ae60" font-weight="bold">采纳话术上线</text>

                    <!-- 关键判断标准说明 -->
                    <rect x="100" y="750" width="1300" height="150" rx="15" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" />
                    <text x="750" y="780" font-size="16" text-anchor="middle" font-weight="bold" fill="#495057">关键判断标准与流程说明</text>

                    <!-- Bad Case 标准 -->
                    <rect x="150" y="800" width="300" height="80" rx="10" fill="white" stroke="#e74c3c" stroke-width="2" />
                    <text x="300" y="825" font-size="14" text-anchor="middle" font-weight="bold" fill="#e74c3c">Bad Case 判断标准</text>
                    <text x="300" y="845" font-size="12" text-anchor="middle" fill="#555">• 系统异常、响应超时、轮次过少</text>
                    <text x="300" y="860" font-size="12" text-anchor="middle" fill="#555">• 话术不匹配、意图分类错误</text>

                    <!-- Good Case 标准 -->
                    <rect x="480" y="800" width="300" height="80" rx="10" fill="white" stroke="#27ae60" stroke-width="2" />
                    <text x="630" y="825" font-size="14" text-anchor="middle" font-weight="bold" fill="#27ae60">Good Case 判断标准</text>
                    <text x="630" y="845" font-size="12" text-anchor="middle" fill="#555">• 会话轮次 > 4轮</text>
                    <text x="630" y="860" font-size="12" text-anchor="middle" fill="#555">• 客户意图：负面→正面转化</text>

                    <!-- 专家审核机制 -->
                    <rect x="810" y="800" width="300" height="80" rx="10" fill="white" stroke="#7b1fa2" stroke-width="2" />
                    <text x="960" y="825" font-size="14" text-anchor="middle" font-weight="bold" fill="#7b1fa2">专家审核机制</text>
                    <text x="960" y="845" font-size="12" text-anchor="middle" fill="#555">• 采纳：无需理由，直接上线</text>
                    <text x="960" y="860" font-size="12" text-anchor="middle" fill="#555">• 否决：需备注理由 / 修改后采纳</text>

                    <!-- 数据存储 -->
                    <rect x="1140" y="800" width="250" height="80" rx="10" fill="white" stroke="#ff9800" stroke-width="2" />
                    <text x="1265" y="825" font-size="14" text-anchor="middle" font-weight="bold" fill="#ff9800">数据存储</text>
                    <text x="1265" y="845" font-size="12" text-anchor="middle" fill="#555">• VOC落地存储所有记录</text>
                    <text x="1265" y="860" font-size="12" text-anchor="middle" fill="#555">• 支持效果追踪与分析</text>

                    <!-- 闭环效果指标 -->
                    <rect x="100" y="950" width="1300" height="180" rx="15" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" />
                    <text x="750" y="980" font-size="16" text-anchor="middle" font-weight="bold" fill="#2e7d32">闭环效果预期指标</text>

                    <!-- 指标卡片 -->
                    <rect x="150" y="1000" width="240" height="100" rx="10" fill="white" stroke="#4caf50" stroke-width="2" />
                    <text x="270" y="1025" font-size="14" text-anchor="middle" font-weight="bold" fill="#4caf50">Good Case识别率</text>
                    <text x="270" y="1050" font-size="24" text-anchor="middle" font-weight="bold" fill="#4caf50">≥15%</text>
                    <text x="270" y="1075" font-size="12" text-anchor="middle" fill="#555">每日外呼中优质案例</text>
                    <text x="270" y="1090" font-size="12" text-anchor="middle" fill="#555">意图转化成功率</text>

                    <rect x="420" y="1000" width="240" height="100" rx="10" fill="white" stroke="#ff9800" stroke-width="2" />
                    <text x="540" y="1025" font-size="14" text-anchor="middle" font-weight="bold" fill="#ff9800">话术萃取效率</text>
                    <text x="540" y="1050" font-size="24" text-anchor="middle" font-weight="bold" fill="#ff9800">≥5条/天</text>
                    <text x="540" y="1075" font-size="12" text-anchor="middle" fill="#555">VOC每日新话术产出</text>
                    <text x="540" y="1090" font-size="12" text-anchor="middle" fill="#555">基于Good Case分析</text>

                    <rect x="690" y="1000" width="240" height="100" rx="10" fill="white" stroke="#9c27b0" stroke-width="2" />
                    <text x="810" y="1025" font-size="14" text-anchor="middle" font-weight="bold" fill="#9c27b0">专家采纳率</text>
                    <text x="810" y="1050" font-size="24" text-anchor="middle" font-weight="bold" fill="#9c27b0">≥60%</text>
                    <text x="810" y="1075" font-size="12" text-anchor="middle" fill="#555">业务专家审核通过</text>
                    <text x="810" y="1090" font-size="12" text-anchor="middle" fill="#555">话术质量保证</text>

                    <rect x="960" y="1000" width="240" height="100" rx="10" fill="white" stroke="#f44336" stroke-width="2" />
                    <text x="1080" y="1025" font-size="14" text-anchor="middle" font-weight="bold" fill="#f44336">话术上线时效</text>
                    <text x="1080" y="1050" font-size="24" text-anchor="middle" font-weight="bold" fill="#f44336">≤24小时</text>
                    <text x="1080" y="1075" font-size="12" text-anchor="middle" fill="#555">从萃取到上线</text>
                    <text x="1080" y="1090" font-size="12" text-anchor="middle" fill="#555">快速响应优化</text>

                    <rect x="1230" y="1000" width="240" height="100" rx="10" fill="white" stroke="#607d8b" stroke-width="2" />
                    <text x="1350" y="1025" font-size="14" text-anchor="middle" font-weight="bold" fill="#607d8b">闭环完整率</text>
                    <text x="1350" y="1050" font-size="24" text-anchor="middle" font-weight="bold" fill="#607d8b">≥90%</text>
                    <text x="1350" y="1075" font-size="12" text-anchor="middle" fill="#555">从识别到上线</text>
                    <text x="1350" y="1090" font-size="12" text-anchor="middle" fill="#555">全流程追踪</text>

                </svg>
            </div>
        </div>

        <!-- 新增：优化版话术闭环流程 -->
        <div style="margin: 50px 0;">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">优化版话术闭环流程</h2>
            <div class="description">
                <h3>三条清晰链路</h3>
                <p><strong>Bad Case链路</strong>：监工Agent→企微告警群→VOC→AI语音外呼平台（有效话术标注）→催收Agent |
                   <strong>Good Case链路1（AI外呼）</strong>：监工Agent→VOC→AI语音外呼平台（有效话术标注）→催收Agent |
                   <strong>Good Case链路2（人工案件）</strong>：线上人工案件→监工Agent→VOC→AI语音外呼平台（新话术推荐+专家审核）→催收Agent |
                   <strong>核心价值</strong>：双源话术优化，Bad Case价值挖掘，全面提升话术质量</p>
            </div>

            <div class="svg-container">
                <svg width="1500" height="1300" viewBox="0 0 1500 1300" xmlns="http://www.w3.org/2000/svg">
                    <!-- 定义箭头样式 -->
                    <defs>
                        <marker id="arrowhead-final" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                        </marker>
                        <marker id="arrowhead-bad-final" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
                        </marker>
                        <marker id="arrowhead-good1-final" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
                        </marker>
                        <marker id="arrowhead-good2-final" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
                        </marker>

                        <!-- 阴影效果 -->
                        <filter id="shadow-final" x="-20%" y="-20%" width="140%" height="140%">
                            <feDropShadow dx="2" dy="3" stdDeviation="3" flood-opacity="0.2" />
                        </filter>

                        <!-- 渐变定义 -->
                        <linearGradient id="platformFinalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e3f2fd; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #bbdefb; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="agentFinalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #fff3e0; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #ffe0b2; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="supervisorFinalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #fce4ec; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #f8bbd9; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="vocFinalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #e8f5e9; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #c8e6c9; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="alertFinalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #ffebee; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #ffcdd2; stop-opacity: 0.9" />
                        </linearGradient>

                        <linearGradient id="manualFinalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color: #f3e5f5; stop-opacity: 1" />
                            <stop offset="100%" style="stop-color: #e1bee7; stop-opacity: 0.9" />
                        </linearGradient>
                    </defs>

                    <!-- 背景框 -->
                    <rect x="50" y="50" width="1370" height="1200" rx="20" fill="none" stroke="#34495e" stroke-width="2" stroke-dasharray="8,4" />
                    <text x="750" y="40" font-size="20" text-anchor="middle" font-weight="bold" fill="#34495e">优化版话术闭环流程</text>

                    <!-- 装饰性分割线 -->
                    <line x1="100" y1="220" x2="1400" y2="220" stroke="#dee2e6" stroke-width="2" stroke-dasharray="5,5" />
                    <text x="550" y="210" font-size="14" text-anchor="middle" font-weight="bold" fill="#6c757d">三条闭环链路</text>

                    <!-- 主要业务流程：顶部整齐布局 -->
                    <!-- 1. AI语音外呼平台 -->
                    <rect x="150" y="80" width="200" height="90" rx="15" fill="url(#platformFinalGradient)" stroke="#1976d2" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="250" y="110" font-size="16" text-anchor="middle" font-weight="bold" fill="#1976d2">AI语音外呼平台</text>
                    <text x="250" y="130" font-size="12" text-anchor="middle" fill="#555">• 催收外呼任务发起</text>
                    <text x="250" y="145" font-size="12" text-anchor="middle" fill="#555">• 专家审核界面</text>
                    <text x="250" y="160" font-size="12" text-anchor="middle" fill="#555">• 异常跟踪打标</text>

                    <!-- 2. IVR -->
                    <rect x="400" y="80" width="200" height="90" rx="15" fill="url(#vocFinalGradient)" stroke="#388e3c" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="500" y="110" font-size="16" text-anchor="middle" font-weight="bold" fill="#388e3c">IVR</text>
                    <text x="500" y="130" font-size="12" text-anchor="middle" fill="#555">• 发起外呼</text>
                    <text x="500" y="145" font-size="12" text-anchor="middle" fill="#555">• 调用催收Agent</text>
                    <text x="500" y="160" font-size="12" text-anchor="middle" fill="#555">• 获取回复话术</text>

                    <!-- 3. 催收Agent -->
                    <rect x="650" y="80" width="200" height="90" rx="15" fill="url(#agentFinalGradient)" stroke="#f57c00" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="750" y="110" font-size="16" text-anchor="middle" font-weight="bold" fill="#f57c00">催收Agent</text>
                    <text x="750" y="130" font-size="12" text-anchor="middle" fill="#555">• 话术决策</text>
                    <text x="750" y="145" font-size="12" text-anchor="middle" fill="#555">• 话术上线</text>
                    <text x="750" y="160" font-size="12" text-anchor="middle" fill="#555">• 生产复验</text>

                    <!-- 4. 监工Agent（中心节点） -->
                    <rect x="900" y="80" width="220" height="90" rx="15" fill="url(#supervisorFinalGradient)" stroke="#c2185b" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="1010" y="110" font-size="16" text-anchor="middle" font-weight="bold" fill="#c2185b">监工Agent</text>
                    <text x="1010" y="130" font-size="12" text-anchor="middle" fill="#555">• 异常检测</text>
                    <text x="1010" y="145" font-size="12" text-anchor="middle" fill="#555">• Good/Bad Case判断</text>
                    <text x="1010" y="160" font-size="12" text-anchor="middle" fill="#555">• 客户意图大类标注</text>

                    <!-- 5. 线上人工案件（新增来源） -->
                    <rect x="1170" y="80" width="200" height="90" rx="15" fill="url(#manualFinalGradient)" stroke="#8e44ad" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="1270" y="110" font-size="16" text-anchor="middle" font-weight="bold" fill="#8e44ad">线上人工案件</text>
                    <text x="1270" y="130" font-size="12" text-anchor="middle" fill="#555">• 人工催收案例</text>
                    <text x="1270" y="145" font-size="12" text-anchor="middle" fill="#555">• 经验萃取</text>
                    <text x="1270" y="160" font-size="12" text-anchor="middle" fill="#555">• 优质话术识别</text>

                    <!-- 主要业务流程连接线 -->
                    <line x1="350" y1="125" x2="400" y2="125" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead-final)" />
                    <text x="375" y="115" font-size="11" text-anchor="middle" fill="#2c3e50" font-weight="bold">调用</text>

                    <line x1="600" y1="125" x2="650" y2="125" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead-final)" />
                    <text x="625" y="115" font-size="11" text-anchor="middle" fill="#2c3e50" font-weight="bold">调用</text>

                    <line x1="850" y1="125" x2="900" y2="125" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead-final)" />
                    <text x="875" y="115" font-size="11" text-anchor="middle" fill="#2c3e50" font-weight="bold">异步调用</text>

                    <!-- 连接线：线上人工案件 -> 监工Agent -->
                    <line x1="1170" y1="125" x2="1120" y2="125" stroke="#8e44ad" stroke-width="3" marker-end="url(#arrowhead-good2-final)" />
                    <text x="1145" y="115" font-size="11" text-anchor="middle" fill="#8e44ad" font-weight="bold">人工案件分析</text>

                    <!-- Bad Case 链路 -->
                    <rect x="120" y="300" width="280" height="40" rx="20" fill="#ffebee" stroke="#e74c3c" stroke-width="2" />
                    <text x="260" y="325" font-size="16" text-anchor="middle" font-weight="bold" fill="#e74c3c">Bad Case 链路</text>

                    <!-- 企微告警群 -->
                    <rect x="150" y="370" width="220" height="90" rx="15" fill="url(#alertFinalGradient)" stroke="#e74c3c" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="260" y="400" font-size="15" text-anchor="middle" font-weight="bold" fill="#e74c3c">企微告警群</text>
                    <text x="260" y="420" font-size="12" text-anchor="middle" fill="#555">• 异常通知</text>
                    <text x="260" y="435" font-size="12" text-anchor="middle" fill="#555">• 分级触达</text>
                    <text x="260" y="450" font-size="12" text-anchor="middle" fill="#555">• 及时响应</text>

                    <!-- VOC话术萃取Agent（Bad Case） -->
                    <rect x="150" y="490" width="220" height="90" rx="15" fill="url(#vocFinalGradient)" stroke="#2e7d32" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="260" y="520" font-size="15" text-anchor="middle" font-weight="bold" fill="#2e7d32">VOC话术萃取Agent</text>
                    <text x="260" y="540" font-size="12" text-anchor="middle" fill="#555">• 异常案例分析</text>
                    <text x="260" y="555" font-size="12" text-anchor="middle" fill="#555">• 话术优化建议</text>
                    <text x="260" y="570" font-size="12" text-anchor="middle" fill="#555">• 价值挖掘</text>

                    <!-- AI语音外呼平台（Bad Case） -->
                    <rect x="150" y="610" width="220" height="90" rx="15" fill="url(#platformFinalGradient)" stroke="#1976d2" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="260" y="640" font-size="15" text-anchor="middle" font-weight="bold" fill="#1976d2">AI语音外呼平台</text>
                    <text x="260" y="660" font-size="12" text-anchor="middle" fill="#555">• 有效话术标注</text>
                    <text x="260" y="675" font-size="12" text-anchor="middle" fill="#555">• 异常跟踪打标</text>
                    <text x="260" y="690" font-size="12" text-anchor="middle" fill="#555">• 状态管理</text>

                    <!-- 催收Agent（Bad Case） -->
                    <rect x="150" y="730" width="220" height="90" rx="15" fill="url(#agentFinalGradient)" stroke="#f57c00" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="260" y="760" font-size="15" text-anchor="middle" font-weight="bold" fill="#f57c00">催收Agent</text>
                    <text x="260" y="780" font-size="12" text-anchor="middle" fill="#555">• 生产复验</text>
                    <text x="260" y="795" font-size="12" text-anchor="middle" fill="#555">• 效果验证</text>
                    <text x="260" y="810" font-size="12" text-anchor="middle" fill="#555">• 持续改进</text>

                    <!-- Bad Case 连接线 -->
                    <path d="M 1010,170 L 1010,240 L 260,240 L 260,300" fill="none" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead-bad-final)" />
                    <text x="635" y="230" font-size="13" text-anchor="middle" fill="#e74c3c" font-weight="bold">Bad Case</text>

                    <line x1="260" y1="460" x2="260" y2="490" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-bad-final)" />
                    <text x="300" y="480" font-size="12" text-anchor="middle" fill="#e74c3c" font-weight="bold">异常分析</text>

                    <line x1="260" y1="580" x2="260" y2="610" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-bad-final)" />
                    <text x="310" y="600" font-size="12" text-anchor="middle" fill="#e74c3c" font-weight="bold">有效话术标注</text>

                    <line x1="260" y1="700" x2="260" y2="730" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-bad-final)" />
                    <text x="300" y="720" font-size="12" text-anchor="middle" fill="#e74c3c" font-weight="bold">生产复验</text>

                    <!-- Good Case 链路1标题 -->
                    <rect x="550" y="300" width="320" height="40" rx="18" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" />
                    <text x="710" y="325" font-size="16" text-anchor="middle" font-weight="bold" fill="#27ae60">🟢 Good Case 链路1（AI外呼来源）</text>

                    <!-- VOC话术萃取Agent（Good Case 1） -->
                    <rect x="580" y="370" width="260" height="90" rx="15" fill="url(#vocFinalGradient)" stroke="#2e7d32" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="710" y="395" font-size="15" text-anchor="middle" font-weight="bold" fill="#2e7d32">VOC话术萃取Agent</text>
                    <text x="710" y="415" font-size="12" text-anchor="middle" fill="#555">• AI外呼案例分析</text>
                    <text x="710" y="430" font-size="12" text-anchor="middle" fill="#555">• 有效话术萃取</text>
                    <text x="710" y="445" font-size="12" text-anchor="middle" fill="#555">• 质量评估</text>

                    <!-- AI语音外呼平台（Good Case 1） -->
                    <rect x="580" y="490" width="260" height="90" rx="15" fill="url(#platformFinalGradient)" stroke="#1976d2" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="710" y="520" font-size="15" text-anchor="middle" font-weight="bold" fill="#1976d2">AI语音外呼平台</text>
                    <text x="710" y="540" font-size="12" text-anchor="middle" fill="#555">• 有效话术标注</text>
                    <text x="710" y="555" font-size="12" text-anchor="middle" fill="#555">• 确认上线</text>
                    <text x="710" y="570" font-size="12" text-anchor="middle" fill="#555">• 效果跟踪</text>

                    <!-- 催收Agent（Good Case 1） -->
                    <rect x="580" y="610" width="260" height="90" rx="15" fill="url(#agentFinalGradient)" stroke="#f57c00" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="710" y="635" font-size="15" text-anchor="middle" font-weight="bold" fill="#f57c00">催收Agent</text>
                    <text x="710" y="655" font-size="12" text-anchor="middle" fill="#555">• 话术上线</text>
                    <text x="710" y="670" font-size="12" text-anchor="middle" fill="#555">• 生产应用</text>
                    <text x="710" y="685" font-size="12" text-anchor="middle" fill="#555">• 效果监控</text>

                    <!-- Good Case 1 连接线 -->
                    <path d="M 1010,170 L 1010,210 L 710,210 L 710,300" fill="none" stroke="#27ae60" stroke-width="4" marker-end="url(#arrowhead-good1-final)" />
                    <text x="800" y="200" font-size="13" text-anchor="middle" fill="#27ae60" font-weight="bold">Good Case（AI外呼）</text>

                    <line x1="710" y1="460" x2="710" y2="490" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead-good1-final)" />
                    <text x="760" y="477" font-size="12" text-anchor="middle" fill="#27ae60" font-weight="bold">有效话术标注</text>

                    <line x1="710" y1="580" x2="710" y2="610" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead-good1-final)" />
                    <text x="760" y="597" font-size="12" text-anchor="middle" fill="#27ae60" font-weight="bold">话术上线</text>

                    <!-- Good Case 链路2标题 -->
                    <rect x="980" y="300" width="360" height="40" rx="18" fill="#e3f2fd" stroke="#3498db" stroke-width="2" />
                    <text x="1160" y="325" font-size="16" text-anchor="middle" font-weight="bold" fill="#3498db">🔵 Good Case 链路2（人工案件来源）</text>

                    <!-- VOC话术萃取Agent（Good Case 2） -->                  
                    <rect x="1020" y="370" width="280" height="90" rx="15" fill="url(#vocFinalGradient)" stroke="#2e7d32" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="1160" y="395" font-size="15" text-anchor="middle" font-weight="bold" fill="#2e7d32">VOC话术萃取Agent</text>
                    <text x="1160" y="415" font-size="12" text-anchor="middle" fill="#555">• 人工案例分析</text>
                    <text x="1160" y="430" font-size="12" text-anchor="middle" fill="#555">• 经验话术萃取</text>
                    <text x="1160" y="445" font-size="12" text-anchor="middle" fill="#555">• 创新话术生成</text>

                    <!-- AI语音外呼平台（Good Case 2） -->
                    <rect x="1020" y="490" width="280" height="90" rx="15" fill="url(#platformFinalGradient)" stroke="#1976d2" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="1160" y="520" font-size="15" text-anchor="middle" font-weight="bold" fill="#1976d2">AI语音外呼平台</text>
                    <text x="1160" y="540" font-size="12" text-anchor="middle" fill="#555">• 新话术推荐</text>
                    <text x="1160" y="555" font-size="12" text-anchor="middle" fill="#555">• 专家审核</text>
                    <text x="1160" y="570" font-size="12" text-anchor="middle" fill="#555">• 质量把控</text>

                    <!-- 催收Agent（Good Case 2） -->
                    <rect x="1020" y="610" width="280" height="90" rx="15" fill="url(#agentFinalGradient)" stroke="#f57c00" stroke-width="3" filter="url(#shadow-final)" />
                    <text x="1160" y="635" font-size="15" text-anchor="middle" font-weight="bold" fill="#f57c00">催收Agent</text>
                    <text x="1160" y="655" font-size="12" text-anchor="middle" fill="#555">• 话术上线</text>
                    <text x="1160" y="670" font-size="12" text-anchor="middle" fill="#555">• 生产应用</text>
                    <text x="1160" y="685" font-size="12" text-anchor="middle" fill="#555">• 效果评估</text>

                    <!-- Good Case 2 连接线 -->
                    <path d="M 1010,170 L 1010,210 L 1160,210 L 1160,300" fill="none" stroke="#3498db" stroke-width="4" marker-end="url(#arrowhead-good2-final)" />
                    <text x="1100" y="200" font-size="13" text-anchor="middle" fill="#3498db" font-weight="bold">Good Case（人工案件）</text>

                    <line x1="1160" y1="460" x2="1160" y2="490" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-good2-final)" />
                    <text x="1230" y="477" font-size="12" text-anchor="middle" fill="#3498db" font-weight="bold">新话术推荐+专家审核</text>

                    <line x1="1160" y1="580" x2="1160" y2="610" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-good2-final)" />
                    <text x="1220" y="597" font-size="12" text-anchor="middle" fill="#3498db" font-weight="bold">话术上线</text>

                    <!-- 流程说明 -->
                    <rect x="100" y="850" width="1200" height="260" rx="15" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" />
                    <text x="700" y="880" font-size="18" text-anchor="middle" font-weight="bold" fill="#495057">优化版话术闭环流程说明</text>

                    <!-- 核心变化说明 -->
                    <rect x="150" y="860" width="350" height="100" rx="8" fill="white" stroke="#e74c3c" stroke-width="2" />
                    <text x="325" y="885" font-size="14" text-anchor="middle" font-weight="bold" fill="#e74c3c">Bad Case链路优化</text>
                    <text x="325" y="905" font-size="12" text-anchor="middle" fill="#555">监工Agent → 企微告警群 → VOC → AI语音外呼平台 → 催收Agent</text>
                    <text x="325" y="925" font-size="12" text-anchor="middle" fill="#555">• 企微告警群后增加VOC话术萃取环节</text>
                    <text x="325" y="940" font-size="12" text-anchor="middle" fill="#555">• 异常案例价值挖掘，变废为宝</text>

                    <rect x="525" y="860" width="350" height="100" rx="8" fill="white" stroke="#27ae60" stroke-width="2" />
                    <text x="700" y="885" font-size="14" text-anchor="middle" font-weight="bold" fill="#27ae60">Good Case链路1（AI外呼）</text>
                    <text x="700" y="905" font-size="12" text-anchor="middle" fill="#555">监工Agent → VOC → AI语音外呼平台 → 催收Agent</text>
                    <text x="700" y="925" font-size="12" text-anchor="middle" fill="#555">• 有效话术标注</text>
                    <text x="700" y="940" font-size="12" text-anchor="middle" fill="#555">• AI外呼优质案例，确认后上线</text>

                    <rect x="900" y="860" width="350" height="100" rx="8" fill="white" stroke="#3498db" stroke-width="2" />
                    <text x="1075" y="885" font-size="14" text-anchor="middle" font-weight="bold" fill="#3498db">Good Case链路2（人工案件）</text>
                    <text x="1075" y="905" font-size="12" text-anchor="middle" fill="#555">线上人工案件 → 监工Agent → VOC → AI语音外呼平台 → 催收Agent</text>
                    <text x="1075" y="925" font-size="12" text-anchor="middle" fill="#555">• 新增人工案件来源</text>
                    <text x="1075" y="940" font-size="12" text-anchor="middle" fill="#555">• 经验话术萃取，专家审核后上线</text>

                    <!-- 整体价值 -->
                    <rect x="300" y="980" width="800" height="100" rx="8" fill="white" stroke="#8e44ad" stroke-width="2" />
                    <text x="700" y="1005" font-size="16" text-anchor="middle" font-weight="bold" fill="#8e44ad">整体价值提升</text>
                    <text x="700" y="1030" font-size="14" text-anchor="middle" fill="#555">• 双源话术优化：AI外呼 + 人工案件两个来源，全面覆盖</text>
                    <text x="700" y="1050" font-size="14" text-anchor="middle" fill="#555">• Bad Case价值挖掘：异常案例也能产生话术优化价值，变废为宝</text>
                    <text x="700" y="1070" font-size="14" text-anchor="middle" fill="#555">• 差异化处理：不同来源采用不同审核策略，精准高效</text>

                </svg>
            </div>
        </div>

    </div>



    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
