# AI监工2.0系统流程图

## 系统概述
基于现有监工链路优化，新增多维度异常监控能力，实现实时+事后双重监控机制。

## 监工2.0流程图

```mermaid
flowchart TD
    A[数据入口] --> B{数据来源}
    B -->|实时数据| C[实时监控链路]
    B -->|通话结束数据| D[事后监控链路]
    
    %% 实时监控链路（沿用现有）
    C --> C1[意图结果处理]
    C1 --> C2[LLM: 意图初检]
    C1 --> C3[RAG: 意图语料库召回]
    C1 -.-> C4[策略检查<br/>重复检查等]
    
    C2 --> C5[实时异常检测]
    C3 --> C5
    C4 -.-> C5
    
    C5 --> C6{检测到异常?}
    C6 -->|是| C7[LLM: 复检+错误原因分析]
    C6 -->|否| C8[继续监控]
    
    C7 --> C9[企微实时触达]
    C8 --> C10[数据暂存]
    C9 --> C10
    
    %% 事后监控链路（新增）
    D --> D1[会话数据完整性检查]
    D1 --> D2[多维度异常分析]
    
    D2 --> D3[系统异常检测]
    D2 --> D4[业务异常检测]
    D2 --> D5[话术质量检测]
    
    %% 系统异常检测
    D3 --> D3a[响应耗时检测<br/>≥3秒超时]
    D3 --> D3b[系统静默检测<br/>≥5秒无响应]
    D3 --> D3c[系统报错检测<br/>异常日志分析]
    
    %% 业务异常检测
    D4 --> D4a[轮次异常检测<br/>≤4轮挂断]
    D4 --> D4b[异常挂断检测<br/>未命中结束语]
    D4 --> D4c[通时异常检测<br/>≥5分钟通话]
    
    %% 话术质量检测
    D5 --> D5a[话术匹配检测<br/>牛头不对马嘴]
    D5 --> D5b[意图分类检测<br/>一级意图准确性]
    D5 --> D5c[话术优化检测<br/>更优模型评估]
    D5 --> D5d[话术评分检测<br/>质量阈值判断]
    
    %% 异常汇总与处理
    D3a --> E[异常结果汇总]
    D3b --> E
    D3c --> E
    D4a --> E
    D4b --> E
    D4c --> E
    D5a --> E
    D5b --> E
    D5c --> E
    D5d --> E
    
    E --> F[LLM: 综合分析+优化建议]
    F --> G[异常分类标记]
    
    G --> H{触达方式选择}
    H -->|实时异常| H1[企微即时触达]
    H -->|批量异常| H2[企微批量推送]
    H -->|数据记录| H3[系统埋点落库]
    
    H1 --> I[监控结果展示]
    H2 --> I
    H3 --> I
    
    %% 数据流转与存储
    C10 --> J[数据汇聚层]
    I --> J
    J --> K[触达系统接口]
    K --> L[异常数据展示]
    
    %% 样式定义
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#e1f5fe,stroke:#333,stroke-width:2px
    style D fill:#fff3e0,stroke:#333,stroke-width:2px
    style E fill:#f3e5f5,stroke:#333,stroke-width:2px
    style F fill:#e8f5e8,stroke:#333,stroke-width:2px
    style G fill:#fff9c4,stroke:#333,stroke-width:2px
    style I fill:#ffebee,stroke:#333,stroke-width:2px
    style L fill:#e0f2f1,stroke:#333,stroke-width:2px
```

## 异常类型定义

### 1. 系统异常类型
- **响应超时**: 灵犀响应时间≥3秒
- **系统静默**: 系统无响应时间≥5秒  
- **系统报错**: 捕获到系统异常日志

### 2. 业务异常类型
- **轮次过少**: 系统侧挂断但轮次≤4轮
- **异常挂断**: 系统侧挂断但未命中结束语
- **通时过长**: 单次通话时长≥5分钟

### 3. 话术质量异常
- **话术不匹配**: 回复内容与用户意图不符
- **意图分类错误**: 一级意图大类识别错误
- **话术非最优**: 存在更优话术选择
- **话术评分过低**: 话术质量评分低于阈值

## 技术实现要点

### 1. 实时监控链路优化
- 保持现有监控逻辑不变
- 增强异常检测算法精度
- 优化LLM调用效率

### 2. 事后监控链路设计
- 基于会话结束事件触发
- 多维度并行异常检测
- 智能异常聚合与分析

### 3. 数据流转机制
- 统一数据接口标准
- 异步处理提升性能
- 容错机制保障稳定性

### 4. 展示与触达
- 新增异常类型Tab页
- 支持实时和批量触达
- 提供详细异常描述和建议

## 部署建议

### 阶段一：基础能力建设（1-2周）
- 完善现有实时监控链路
- 开发事后监控核心模块
- 建立异常数据模型

### 阶段二：功能集成测试（2-3周）  
- 集成多维度异常检测
- 完善触达机制
- 系统联调测试

### 阶段三：上线与优化（1-2周）
- 灰度发布验证
- 性能调优
- 用户反馈收集与优化
